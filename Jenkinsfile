pipeline {
  agent {
    kubernetes {
      inheritFrom 'default-agent'
      yaml """
      apiVersion: v1
      kind: Pod
      spec:
        containers:
        - name: docker-client
          image: docker:20
          command: ['sleep', '99d']
          env:
            - name: DOCKER_HOST
              value: tcp://localhost:2375
        - name: aws-cli
          image: amazon/aws-cli:latest
          command: ['sleep', '99d']  
        - name: docker-daemon
          image: docker:20-dind
          resources:
            limits:
              memory: "4Gi"
              cpu: "1"
          env:
            - name: DOCKER_TLS_CERTDIR
              value: ""
          securityContext:
            privileged: true
          volumeMounts:
            - name: cache
              mountPath: /var/lib/docker
        - name: snyk-docker
          image: snyk/snyk:docker
          command: ['sleep', '99d']
          env:
            - name: DOCKER_HOST
              value: tcp://localhost:2375
        volumes:
        - name: cache
          emptyDir: {}
      """
    }
  }
  environment {
    FULL_ECR_REPO = "411985166407.dkr.ecr.us-east-1.amazonaws.com/esource/apc-restoration-job"
    REPOSITORY_ECR = "411985166407.dkr.ecr.us-east-1.amazonaws.com"
    NS_IMG_NAME = "esource/apc-restoration-job"

    VERSION = sh(script: "echo `date +%Y%m%d%H%M%S`", returnStdout: true).trim()
    // sanitize branch names for use in docker tags, PRs will have PR in the name
    ESCAPED_BRANCH = sh(script: 'echo $GIT_BRANCH | sed "s=/=_=g"', returnStdout: true).trim()
    timestamp = sh(script: 'date +%s', returnStdout: true).trim()
    BUILD_IMAGE_TAG = "${GIT_COMMIT[0..7]}-${timestamp}"
  }

  parameters {
    // Default: High - Should never be changed below high unless DEBUG_DEV_RUN = true
    choice(
      name: 'SNYK_SEVERITY_THRESHOLD',
      description: 'SNYK Severity Threshold - See: https://docs.snyk.io/manage-issues/issue-management/severity-levels',
      choices: ['high', 'critical', 'medium', 'low', 'trivial']
    )

    // Default: False
    choice(
      name: 'DEBUG_DEV_RUN',
      description: 'Development Test - Useful for testing changes',
      choices: ['false', 'true']
    )

    // Default: True
    choice(
      name: 'DEBUG_FAIL_ON_SNYK_TESTING',
      description: 'Does Not Fail The Build If SNYK Testing Fails - Only Applies To Dev Tests, DEBUG_DEV_TEST = true',
      choices: ['true', 'false']
    )

    choice(
      name: 'MANUAL_BUILD_APC',
      description: 'Manually Builds At The Current Branch. Does Not Include Latest Automatic',
      choices: ['false', 'true']
    )
    string(name: "DOCKER_BUILD_OPTS", defaultValue: "--no-cache", description: "Docker build options")
  }

  stages {
    stage('Build Parameter Checks') {
      steps {
        script {
          if (!(env.SNYK_SEVERITY_THRESHOLD == 'high' || env.SNYK_SEVERITY_THRESHOLD == 'critical') && env.DEBUG_DEV_RUN == 'false') {
            error('SNYK_SEVERITY_THRESHOLD cannot be below high unless DEBUG_DEV_RUN is true')
          }
          if (env.DEBUG_DEV_RUN == 'false' && env.DEBUG_FAIL_ON_SNYK_TESTING == 'false') {
            error('DEBUG_FAIL_ON_SNYK_TESTING cannot be false unless DEBUG_DEV_RUN is true')
          }
          manualBuild = env.MANUAL_BUILD_APC == 'true'
          if (manualBuild == 'true' && env.DEBUG_FAIL_ON_SNYK_TESTING == 'false') {
            error('Cannot Do Manual Builds Without Snyk Testing Failure')
          }
          // sanitize docker build options
          if (env.DOCKER_BUILD_OPTS == null) {
            env.DOCKER_BUILD_OPTS = " "
          }
        }
      }
    }
    stage('Login to Docker repositories') {
      steps {
        script {
          container("aws-cli") {
            docker_pwd = sh(script: 'aws ecr get-login-password --region us-east-1', returnStdout: true)
          }
          container("docker-client") {
            sh "echo \"${docker_pwd}\" | docker login -u AWS --password-stdin ${REPOSITORY_ECR}"
          }
        }
      }
    }
    stage('Login to AWS Codeartifact') {
      steps {
        script {
          container("aws-cli"){
            env.CODE_ARTIFACT_TOKEN = sh(script: 'aws codeartifact get-authorization-token --domain esource-int-artifacts --domain-owner 411985166407 --region us-east-1 --query authorizationToken --output text', returnStdout: true)
          }
        }
      }
    }
    stage('Build APC Image') {
      when {
        anyOf {
          allOf {
            anyOf {
              branch 'development'
              branch 'staging'
            }
          }
          expression {
            return env.DEBUG_DEV_RUN == 'true' || env.MANUAL_BUILD_APC == 'true'
          }
        }
      }
      steps {
        script {
          apc = "${NS_IMG_NAME}:${VERSION}"
          buildScanAndPushImage(apc, 'Dockerfile')
        }
      }
    }
    stage('Deploy: APC Azure CR (DV)') {
      when { branch 'development' }
      steps {
        container('docker-client') {
          script {
            dv_repository = "aprampdvacr.azurecr.io"
            withCredentials([usernamePassword(credentialsId: 'apc_azurecr_credentials', usernameVariable: 'DOCKER_USERNAME', passwordVariable: 'DOCKER_PASSWORD')]) {
              sh "docker login -u $DOCKER_USERNAME -p $DOCKER_PASSWORD ${dv_repository}"
            }
            sh "docker tag $NS_IMG_NAME:$VERSION ${dv_repository}/$NS_IMG_NAME:$VERSION"
            sh "docker push ${dv_repository}/$NS_IMG_NAME:$VERSION"
            sh "docker tag $NS_IMG_NAME:$VERSION ${dv_repository}/$NS_IMG_NAME:$ESCAPED_BRANCH"
            sh "docker push ${dv_repository}/$NS_IMG_NAME:$ESCAPED_BRANCH"
            sh "docker tag $NS_IMG_NAME:$VERSION ${dv_repository}/$NS_IMG_NAME:latest"
            sh "docker push ${dv_repository}/$NS_IMG_NAME:latest"
          }
        }
      }
    }
    stage('Deploy: APC Azure CR (UA)') {
      when { branch 'staging' }
      steps {
        container('docker-client') {
          script {
            ua_repository = "aprampuaacr.azurecr.io"
            withCredentials([usernamePassword(credentialsId: 'apc_ua_azurecr_credentials', usernameVariable: 'DOCKER_USERNAME', passwordVariable: 'DOCKER_PASSWORD')]) {
              sh "docker login -u $DOCKER_USERNAME -p $DOCKER_PASSWORD ${ua_repository}"
            }
            sh "docker tag $NS_IMG_NAME:$VERSION ${ua_repository}/$NS_IMG_NAME:$VERSION"
            sh "docker push ${ua_repository}/$NS_IMG_NAME:$VERSION"
            sh "docker tag $NS_IMG_NAME:$VERSION ${ua_repository}/$NS_IMG_NAME:$ESCAPED_BRANCH"
            sh "docker push ${ua_repository}/$NS_IMG_NAME:$ESCAPED_BRANCH"
            sh "docker tag $NS_IMG_NAME:$VERSION ${ua_repository}/$NS_IMG_NAME:latest"
            sh "docker push ${ua_repository}/$NS_IMG_NAME:latest"
          }
        }
      }
    }
    stage('Latest Automation') {
      when {
        anyOf {
          branch 'main'
        }
      }
      steps {
        script {
          container("docker-client") {
            // Construct the staging tag and the latest tag for the current region
            stagingTag = "${env.FULL_ECR_REPO}:staging"
            latestTag = "${REPOSITORY_ECR}/${NS_IMG_NAME}:latest"

            // Pull the staging image
            sh "docker pull ${stagingTag}"

            // Retag the staging image as the latest image
            sh "docker tag ${stagingTag} ${latestTag}"
            
            // Push the latest image to the ECR repository
            sh "docker push ${latestTag}"
          }
        }
      }
    }
  }
  post {
    always {
      container("docker-client") {
        sh 'docker logout'
      }
    }
  }
}

/**
 * Performs the following funcitons:
 * 1. Builds a Docker image
 * 2. Performs a security scan on the Docker image
 * 3. Tags and pushes the Docker image to various registries
 *
 * @param imageName   The name of the Docker image to build, scan, and push. e.g  esource/storm-etr-image:base-20210101010101
 * @param dockerFile  The path to the Dockerfile used to build the Docker image.
 * 
 * @throws Exception  If there is an error during the security scan of the Docker image.
 */
def buildScanAndPushImage(imageName, dockerFile) {
  println "==================================\nBuilding Image: ${imageName}\n=================================="
  buildImage(imageName, dockerFile)

  println "==================================\nScanning Image: ${imageName}\n=================================="
  if (DEBUG_FAIL_ON_SNYK_TESTING == 'true') {
    securityScanImage(imageName, dockerFile)
  } else {
    // debug mode, do not fail the build if snyk testing fails
    try {
      securityScanImage(imageName, dockerFile)
      println "==================================\nScanning PASS: ${imageName}\n=================================="
    } catch (Exception e) {
      println "==================================\nSNYK Scan Failed: ${imageName}\n=================================="
      echo "${e}"
    }
  }
  println "==================================\nTagging and Pushing: ${imageName}\n=================================="
  tagAndPushImage(imageName)
}

/**
 * Builds a Docker image using the specified Dockerfile and tags the image with the specified name.
 *
 * @param buildDir    The Jenkins build context directory object for this build.
 * @param imageName   The name of the Docker image to build, scan, and push.
 * @param dockerFile  The path to the Dockerfile used to build the Docker image.
 */
def buildImage(imageName, dockerFile) {
    container('docker-daemon') {
      sh """
      docker build . \
        -t ${imageName} ${env.DOCKER_BUILD_OPTS} \
        --build-arg CODE_ARTIFACT_TOKEN=${env.CODE_ARTIFACT_TOKEN}
      """
    }
}

/**
 * Scans a Docker image for security vulnerabilities using Snyk and the specified Dockerfile.
 *
 * @param imageName   The name of the Docker image to scan. eg. esource/storm-etr-image:base-20230412001003
 * @param dockerFile  The path to the Dockerfile used to build the Docker image.
 *
 * @throws Exception  If there is an error during the security scan of the Docker image.
 */
def securityScanImage(imageName, dockerFile) {
  echo "Scanning image: ${imageName} with dockerfile: ${dockerFile}"
  container("snyk-docker") {
    withCredentials([string(credentialsId: 'snyk_token', variable: 'SNYK_TOKEN')]) {
      sh """
      snyk container test ${imageName} \
        --file=${dockerFile} \
        --severity-threshold=${env.SNYK_SEVERITY_THRESHOLD} \
        --project-name=${env.NS_IMG_NAME} \
        --fail-on=upgradable \
        --show-vulnerable-paths=all \
        --print-deps \
        --color
      """
    }
  }
}

/**
 * Tags a Docker image with the specified names and pushes the image to various Docker registries.
 *
 * @param region       The base region name of the Docker image to tag and push. e.g 'base', 'dlc'
 * @param baseImage   The base name of the Docker image to tag and push. e.g esource/storm-etr-image:base-20230412001003
 */
def tagAndPushImage(baseImage) {
  // sanitize branch names for use in docker tags, PRs will have PR in the name
  escapedBranch = env.ESCAPED_BRANCH

  //e.g 411985166407.dkr.ecr.us-east-1.amazonaws.com/storm-etr-image:base
  baseTag = "${env.FULL_ECR_REPO}:"

  //e.g 411985166407.dkr.ecr.us-east-1.amazonaws.com/storm-etr-image:base-development
  //e.g 411985166407.dkr.ecr.us-east-1.amazonaws.com/storm-etr-image:base-staging
  ecrBranchTag = "${baseTag}${escapedBranch}"

  //e.g 411985166407.dkr.ecr.us-east-1.amazonaws.com/storm-etr-image:base-20230412001003
  ecrTagWithVer = "${baseTag}${escapedBranch}-${BUILD_IMAGE_TAG}"

  container("docker-client") {
    sh "docker tag ${baseImage} ${ecrBranchTag}"
    sh "docker tag ${baseImage} ${ecrTagWithVer}"
      
    sh "docker push ${ecrBranchTag}"
    sh "docker push ${ecrTagWithVer}"
  }
}

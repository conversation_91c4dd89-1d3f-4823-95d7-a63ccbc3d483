#####################################
# Python Package Configuration file #
#####################################

# Python packages are installed via pip. To add a new package, enter the package name on a new
# line at the approriate location (packages are sorted alphabetically).
# esource_etr_writer==3.7.3
# esource_etr_reader==0.3
# esource-rabbitmq-writer
esource_etr_writer==3.7.5
esource_results_writer==1.9.2
pandas
pymongo
pyodbc==5.1.0
requests
setuptools>=70.0
boto3>=1.26.73
databricks-sql-connector
pyarrow
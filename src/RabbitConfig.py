from dataclasses import dataclass
import os
import pika

from Singleton import Singleton


@dataclass
class RabbitConfig(metaclass=Singleton):
    host: str = "localhost"
    username: str = "trove"
    password: str = "trove"
    port: int = 5672
    enabled: bool = False

    def __post_init__(self):
        self.populate_from_env()

    def populate_from_env(self):
        self.host = os.getenv("RABBIT_HOST", self.host)
        self.port = int(os.getenv("RABBIT_PORT", self.port))
        self.username = os.getenv("RABBIT_USERNAME", self.username)
        self.password = os.getenv("RABBIT_PASSWORD", self.password)

    def __str__(self):
        return f"{self.username}:{self.password}@{self.host}:{self.port} ({self.enabled})"

    def get_rabbit_connection_params(self):
        credentials = pika.PlainCredentials(self.username, self.password)
        return pika.ConnectionParameters(
            host=self.host, port=self.port, credentials=credentials
        )

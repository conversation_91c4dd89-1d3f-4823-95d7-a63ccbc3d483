import logging
import os
import sys

from datetime import datetime
import pandas as pd
import numpy as np
import pyodbc
import requests
import time
import uuid
from dateutil import tz
from databricks import sql
from pandas import DataFrame
from ResultsWriter.Storm.StormWriter import (DailyDocumentBuilder, 
                                            HourlyDocumentBuilder,
                                            StormSessionBuilder)
# --------------------
# ENVIRONMENT
# --------------------

# Logging Setup
logging.basicConfig(format="%(asctime)s %(levelname)s [%(name)s] %(message)s")
logging.getLogger().setLevel(os.environ.get("LOG_LEVEL", logging.INFO))

# Client Auth
CLIENT_AUTHORIZATION = os.getenv("CLIENT_AUTHORIZATION")

#AWS
AWS_ACCESS_TOKEN = os.getenv("AWS_ACCESS_TOKEN")

# Mongo Creds
MONGO_USER = os.getenv("MONGO_USER")
MONGO_PASSWORD = os.getenv("MONGO_PASSWORD")
MONGO_HOST = os.getenv("MONGO_HOST")

# Auth Control Flow
# --------------------
# AZURE SERVICE PRINCIPAL
# --------------------
ACF_TENANT_ID = os.getenv("ACF_TENANT_ID")  # ap-ramp-XX-svcpri-etl.tenant
ACF_CLIENT_ID = os.getenv("ACF_CLIENT_ID")  # ap-ramp-XX-svcpri-etl.appId
ACF_CLIENT_SECRET = os.getenv("ACF_CLIENT_SECRET")  # ap-ramp-XX-svcpri-etl.password
ACF_SCOPE = os.getenv("ACF_SCOPE")  # DBX Service Scope

# Databricks
DBS_HOST = os.getenv("DBS_HOST")
DBS_PORT = os.getenv("DBS_PORT")
DBS_HTTP_PATH = os.getenv("DBS_HTTP_PATH")

# Queries
DBS_CATALOG = os.getenv("DBS_CATALOG")
DBS_SCHEMA = os.getenv("DBS_SCHEMA")
DBS_DAILY_SYSTEM_TABLE = os.getenv("DBS_DAILY_SYSTEM_TABLE")
DBS_DAILY_ZONE_TABLE = os.getenv("DBS_DAILY_ZONE_TABLE")
DBS_DAILY_PARENT_ZONE_TABLE = os.getenv("DBS_DAILY_PARENT_ZONE_TABLE", "es_aggregated_outages_daily_parent_zone")
DBS_HOURLY_SYSTEM_TABLE = os.getenv("DBS_HOURLY_SYSTEM_TABLE")
DBS_HOURLY_WORK_GROUP_TABLE = os.getenv("DBS_HOURLY_WORK_ZONE_SYSTEM_TABLE")
DBS_HOURLY_ZONE_TABLE = os.getenv("DBS_HOURLY_ZONE_SYSTEM_TABLE")

MODEL_VERSION = os.getenv("MODEL_VERSION", "apc-0.1")

# --------------------
# Classes
# --------------------
class TokenRetrievalFailure(Exception):
    pass

# --------------------
# Functions
# --------------------
def retrieve_access_token() -> str:
    url = f"https://login.microsoftonline.com/{ACF_TENANT_ID}/oauth2/v2.0/token"
    data = {
        "client_id": ACF_CLIENT_ID,
        "client_secret": ACF_CLIENT_SECRET,
        "grant_type": "client_credentials",
        "scope": ACF_SCOPE,
    }
    headers = {"Content-Type": "application/x-www-form-urlencoded"}

    try:
        response = requests.post(url, data=data, headers=headers)
        response.raise_for_status()
        content = response.json()
        return content["access_token"]
    except requests.exceptions.RequestException as e:
        logging.error(f"Network error occurred: {e}")
        sys.exit(1)
    except (TokenRetrievalFailure, ValueError, KeyError) as e:
        logging.error(f"Error retrieving access token: {e}")
        sys.exit(1)


def dbs_conn_string_azure(access_token: str) -> str:
    return (
        f"host={DBS_HOST};"
        + f"port={DBS_PORT};"
        + f"httppath={DBS_HTTP_PATH};"
        + f"auth_accesstoken={access_token};"
        + "thrifttransport=2;"
        + "ssl=1;"
        + "authmech=11;"
        + "auth_flow=0;"
        + "driver=/opt/simba/spark/lib/64/libsparkodbc_sb64.so;"
    )


def df_size(df: pd.DataFrame):
    return df.shape[0]


def run_dbs_query(cursor, query: str):
    df = pd.DataFrame()

    try:
        logging.info(f"Running dbx query: '{query}'...")
        cursor.execute(query)
        df = pd.DataFrame.from_records(
            cursor.fetchall(), columns=[col[0] for col in cursor.description]
        )

        if not df.empty:
            logging.info(f"Query returned {df_size(df)} record(s).")
        else:
            logging.warning("Query returned no results.")

    except Exception as e:
        logging.error(e)

    return df

####################
### Daily System ###
####################
def get_daily_system_df():
    daily_system_forecasting_query = f"""
    SELECT * 
    FROM {DBS_CATALOG}.{DBS_SCHEMA}.{DBS_DAILY_SYSTEM_TABLE};
    """  # noqa

    try:
        logging.info(f"Getting daily forecasting system data from {DBS_CATALOG}.{DBS_SCHEMA}.{DBS_DAILY_SYSTEM_TABLE} ...")
        df = run_dbs_query(cursor, daily_system_forecasting_query)

        if df.empty:
            logging.info(
                "The Daily System DataFrame is empty or not found. Skipping processing!"
            )
        else:
            process_daily_system_results(df)

    except Exception as e:
        logging.error(f"Error running query: {e}")

def transform_daily_system_row(row):
    return {
        "day": row["day"].strftime("%Y-%m-%d"),
        "generationTime": row["generation_time"],
        "generationTimeExact": row["generation_time_exact"],
        "location": "apc-system",
        "totalOutages": row["out_pt_est_sum"],
        "totalCustomersAffected": row["ci_pt_est_sum"],
        "timeToDay": row.get("time_to_day", 0),
        "weatherAgg": {
            "precip_inch_sum": row.get("hourly_precipitation_rate_inch_max"),
            "wind_mph_mean": row.get("wind_mph_mean"),
            "gust_mph_amax": row.get("gust_mph_max"),
            "temp_c_amin": row.get("temp_c_min"),
            "temp_c_mean": row.get("temp_c_mean"),
            "temp_c_amax": row.get("temp_c_max"),
            "temp_f_amin": row.get("temp_f_min"),
            "temp_f_mean": row.get("temp_f_mean"),
            "temp_f_amax": row.get("temp_f_max"),
            "spc_sow_amax": row.get("spc_sow_amax"),
            "spc_sow_amax_cat": row["spc_sow_amax_cat"],
            "cloud_cover_percent_mean": row.get("cloud_cover_percent_mean"),
            "wet_hourly_precipitation_rate_inch_max" : row.get("wet_hourly_precipitation_rate_inch_max"),
            "frozen_hourly_precipitation_rate_inch_max" : row.get("frozen_hourly_precipitation_rate_inch_max"),
            "mixed_hourly_precipitation_rate_inch_max" : row.get("mixed_hourly_precipitation_rate_inch_max")
        },
        "resultAgg": {
            "modelVersion": MODEL_VERSION,
            "predictionUpperConfidenceBound": row["out_upper_sum"],
            "medianOutageCount": row["out_pt_est_sum"],
            "predictionLowerConfidenceBound": row["out_lower_sum"],
            "predictionUpperConfidenceBoundCustomersAffected": row["ci_upper_sum"],
            "medianCustomersAffected": row["ci_pt_est_sum"],
            "predictionLowerConfidenceBoundCustomersAffected": row["ci_lower_sum"],
            "weatherOutageCount": np.nan,
            "nonweatherOutageCount": np.nan,
            "weatherOutageCountRestOfDay": np.nan,
            "nonweatherOutageCountRestOfDay": np.nan,
            "submodelPredictions": [],
            "threatProbability": {},
        }
    }

def process_daily_system_results(daily_df : pd.DataFrame) :
    logging.info("Processing daily system results df")
    documents = daily_df.apply(transform_daily_system_row, axis=1)
    write_daily_system_to_mongo(documents)

def write_daily_system_to_mongo(documents):
    generation_time = (
        datetime.now(tz=tz.UTC)
        .replace(minute=0, second=0, microsecond=0)
    )
    
    session_report = StormSessionBuilder(
        targetDate=generation_time,
        targetType= "daily",
        results= {}
    ).build()
    with session_report as s:
        for index, doc in documents.items():
            document_builder = DailyDocumentBuilder(
                session=s.id,
                targetId=str(uuid.uuid4()),
                targetDate=doc["day"],
                results=doc,
                targetType="daily"
            )
            try:
                document_builder.build_and_save()
            except Exception as e:
                logging.error("Failed to save document.")
                logging.error(f"Error saving DAILY document: {e}")
                
####################
### Daily Zone #####
####################
def get_daily_zone_df():
    daily_zone_query = f"""
    SELECT * 
    FROM {DBS_CATALOG}.{DBS_SCHEMA}.{DBS_DAILY_ZONE_TABLE};
    """  # noqa

    try:
        logging.info(f"Getting daily zone data from {DBS_CATALOG}.{DBS_SCHEMA}.{DBS_DAILY_ZONE_TABLE} ...")
        df = run_dbs_query(cursor, daily_zone_query)

        if df.empty:
            logging.info(
                "The Daily Zone DataFrame is empty or not found. Skipping processing!"
            )
        else:
            process_daily_zone_results(df)

    except Exception as e:
        logging.error(f"Error running query: {e}")

def transform_daily_zone_row(row):
    return {
        "day": row["day"].strftime("%Y-%m-%d"),
        "generationTime": row["generation_time"],
        "generationTimeExact": row["generation_time_exact"],
        "location": "apc-zone",
        "totalOutages": row["out_pt_est_sum"],
        "totalCustomersAffected": row["ci_pt_est_sum"],
        "timeToDay": row.get("time_to_day", 0),
        "zone": row["zone"],
        "grouping": row["zone"],
        "displayZone": row["zone"].title(),
        "parentZone": row["parent_zone"] if "parent_zone" in row else None,
        "displayParentZone": row["display_parent_zone"].title() if "display_parent_zone" in row else None,
        "weatherAgg": {
            "precip_inch_sum": row.get("hourly_precipitation_rate_inch_max"),
            "wind_mph_mean": row.get("wind_mph_mean"),
            "gust_mph_amax": row.get("gust_mph_max"),
            "temp_c_amin": row.get("temp_c_min"),
            "temp_c_mean": row.get("temp_c_mean"),
            "temp_c_amax": row.get("temp_c_max"),
            "temp_f_amin": row.get("temp_f_min"),
            "temp_f_mean": row.get("temp_f_mean"),
            "temp_f_amax": row.get("temp_f_max"),
            "spc_sow_amax": row.get("spc_sow_amax"),
            "spc_sow_amax_cat": row["spc_sow_amax_cat"],
            "cloud_cover_percent_mean": row.get("cloud_cover_percent_mean"),
            "wet_hourly_precipitation_rate_inch_max" : row.get("wet_hourly_precipitation_rate_inch_max"),
            "frozen_hourly_precipitation_rate_inch_max" : row.get("frozen_hourly_precipitation_rate_inch_max"),
            "mixed_hourly_precipitation_rate_inch_max" : row.get("mixed_hourly_precipitation_rate_inch_max")
        },
        "resultAgg": {
            "modelVersion": MODEL_VERSION,
            "predictionUpperConfidenceBound": row["out_lower_sum"],
            "medianOutageCount": row["out_pt_est_sum"],
            "predictionLowerConfidenceBound": row["out_upper_sum"],
            "predictionUpperConfidenceBoundCustomersAffected": row["ci_upper_sum"],
            "medianCustomersAffected": row["ci_pt_est_sum"],
            "predictionLowerConfidenceBoundCustomersAffected": row["ci_lower_sum"],
            "weatherOutageCount": np.nan,
            "nonweatherOutageCount": np.nan,
            "weatherOutageCountRestOfDay": np.nan,
            "nonweatherOutageCountRestOfDay": np.nan,
            "submodelPredictions": [],
            "threatProbability": {},
        }
    }

def process_daily_zone_results(daily_zone_df : pd.DataFrame) :
    logging.info("Processing daily zone results df")
    documents = daily_zone_df.apply(transform_daily_zone_row, axis=1)
    write_daily_zone_to_mongo(documents)

def write_daily_zone_to_mongo(documents):
    generation_time = (
        datetime.now(tz=tz.UTC)
        .replace(minute=0, second=0, microsecond=0)
    )
    session_report = StormSessionBuilder(
        targetDate=generation_time,
        targetType= "daily_zone",
        results= {}
    ).build()
    with session_report as s:
        for index, doc in documents.items():
            document_builder = DailyDocumentBuilder(
                session=s.id,
                targetId=str(uuid.uuid4()),
                targetDate=doc["day"], #might have to add in T04? Prith not sure.
                results=doc,
                targetType="daily_zone"
            )
            try:
                document_builder.build_and_save()
            except Exception as e:
                logging.error("Failed to save document.")


#########################
### Daily Parent Zone ###
#########################
def get_daily_parent_zone_df():
    daily_parent_zone_query = f"""
    SELECT * 
    FROM {DBS_CATALOG}.{DBS_SCHEMA}.{DBS_DAILY_PARENT_ZONE_TABLE}; 
    """  
    try:
        logging.info(f"Getting daily parent zone data from {DBS_CATALOG}.{DBS_SCHEMA}.{DBS_DAILY_PARENT_ZONE_TABLE} ...")
        df = run_dbs_query(cursor, daily_parent_zone_query)

        if df.empty:
            logging.info(
                "The Daily Parent Zone DataFrame is empty or not found. Skipping processing!"
            )
        else:
            process_daily_parent_zone_results(df)

    except Exception as e:
        logging.error(f"Error running query: {e}")

def transform_daily_parent_zone_row(row):
    return {
        "day": row["day"].strftime("%Y-%m-%d"),
        "generationTime": row["generation_time"],
        "generationTimeExact": row["generation_time_exact"],
        "location": "apc-zone", #
        "totalOutages": row["out_pt_est_sum"],
        "totalCustomersAffected": row["ci_pt_est_sum"],
        "timeToDay": row.get("time_to_day", 0),
        "parentZone": row["parent_zone"] if "parent_zone" in row else None, 
        "displayParentZone": row["display_parent_zone"].title() if "display_parent_zone" in row else None,
        "weatherAgg": {
            "precip_inch_sum": row.get("hourly_precipitation_rate_inch_max"),
            "wind_mph_mean": row.get("wind_mph_mean"),
            "gust_mph_amax": row.get("gust_mph_max"),
            "temp_c_amin": row.get("temp_c_min"),
            "temp_c_mean": row.get("temp_c_mean"),
            "temp_c_amax": row.get("temp_c_max"),
            "temp_f_amin": row.get("temp_f_min"),
            "temp_f_mean": row.get("temp_f_mean"),
            "temp_f_amax": row.get("temp_f_max"),
            "spc_sow_amax": row.get("spc_sow_amax"),
            "spc_sow_amax_cat": row["spc_sow_amax_cat"],
            "cloud_cover_percent_mean": row.get("cloud_cover_percent_mean")
        },
        "resultAgg": {
            "modelVersion": MODEL_VERSION,
            "predictionUpperConfidenceBound": row["out_lower_sum"],
            "medianOutageCount": row["out_pt_est_sum"],
            "predictionLowerConfidenceBound": row["out_upper_sum"],
            "predictionUpperConfidenceBoundCustomersAffected": row["ci_upper_sum"],
            "medianCustomersAffected": row["ci_pt_est_sum"],
            "predictionLowerConfidenceBoundCustomersAffected": row["ci_lower_sum"],
            "weatherOutageCount": np.nan,
            "nonweatherOutageCount": np.nan,
            "weatherOutageCountRestOfDay": np.nan,
            "nonweatherOutageCountRestOfDay": np.nan,
            "submodelPredictions": [],
            "threatProbability": {},
        }
    }


def process_daily_parent_zone_results(daily_parent_zone_df : pd.DataFrame) :
    logging.info("Processing daily parent zone results df")
    documents = daily_parent_zone_df.apply(transform_daily_parent_zone_row, axis=1)
    write_daily_parent_zone_to_mongo(documents)

def write_daily_parent_zone_to_mongo(documents):
    generation_time = (
        datetime.now(tz=tz.UTC)
        .replace(minute=0, second=0, microsecond=0)
    )
    
    session_report = StormSessionBuilder(
        targetDate=generation_time,
        targetType= "daily_parent_zone", 
        results= {}
    ).build()
    with session_report as s:
        for index, doc in documents.items():
            document_builder = DailyDocumentBuilder(
                session=s.id,
                targetId=str(uuid.uuid4()),
                targetDate=doc["day"], 
                results=doc,
                targetType="daily_parent_zone"
            )
            try:
                document_builder.build_and_save()
            except Exception as e:
                logging.error("Failed to save document.")
                logging.error(f"Error saving DAILY_ZONE document: {e}")
                
####################
### Hourly Zone ####
####################
def get_hourly_zone_df():
    hourly_zone_query = f"""
    SELECT * 
    FROM {DBS_CATALOG}.{DBS_SCHEMA}.{DBS_HOURLY_ZONE_TABLE};
    """  # noqa

    try:
        logging.info(f"Getting hourly zone data from {DBS_CATALOG}.{DBS_SCHEMA}.{DBS_HOURLY_ZONE_TABLE} ...")
        df = run_dbs_query(cursor, hourly_zone_query)

        if df.empty:
            logging.info(
                "The hourly Zone DataFrame is empty or not found. Skipping processing!"
            )
        else:
            process_hourly_zone_results(df)

    except Exception as e:
        logging.error(f"Error running query: {e}")

def transform_hourly_zone_row(row):
    return {
        "targetType": "hourly_zone",
        "targetDate": row["utc_time"],
        "results": {
            "day": row["day"].strftime("%Y-%m-%d"),
            "hour": row["hour"],
            "hour_time": row["utc_time"],
            "generationTime": row["generation_time"],
            "generationTimeExact": row["generation_time_exact"],
            "location": "apc-zone",
            "totalOutages": row["out_pt_est_sum"],
            "totalCustomersAffected": row["ci_pt_est_sum"],
            "zone": row["zone"],
            "grouping": row["zone"],
            "displayZone": row["zone"].title(),
            "parentZone": row["parent_zone"] if "parent_zone" in row else None,
            "displayParentZone": row["display_parent_zone"].title() if "display_parent_zone" in row else None,
            "weatherAgg": {
                "precip_inch_sum": row.get("hourly_precipitation_rate_inch_max"),
                "wind_mph_mean": row.get("wind_mph_mean"),
                "gust_mph_amax": row.get("gust_mph_max"),
                "temp_c_amin": row.get("temp_c_min"),
                "temp_c_mean": row.get("temp_c_mean"),
                "temp_c_amax": row.get("temp_c_max"),
                "temp_f_amin": row.get("temp_f_min"),
                "temp_f_mean": row.get("temp_f_mean"),
                "temp_f_amax": row.get("temp_f_max"),
                "humid_pct_mean": row.get("humid_pct_mean", np.nan),
                "spc_sow_amax": row.get("spc_sow_amax"),
                "spc_sow_amax_cat": row["spc_sow_amax_cat"],
                "cloud_cover_percent_mean": row.get("cloud_cover_percent_mean"),
                "wet_hourly_precipitation_rate_inch_max" : row.get("wet_hourly_precipitation_rate_inch_max"),
                "frozen_hourly_precipitation_rate_inch_max" : row.get("frozen_hourly_precipitation_rate_inch_max"),
                "mixed_hourly_precipitation_rate_inch_max" : row.get("mixed_hourly_precipitation_rate_inch_max")
            },
            "resultAgg": {
                "modelVersion": MODEL_VERSION,
                "predictionUpperConfidenceBound": row["out_lower_sum"],
                "medianOutageCount": row["out_pt_est_sum"],
                "predictionLowerConfidenceBound": row["out_upper_sum"],
                "predictionUpperConfidenceBoundCustomersAffected": row["ci_upper_sum"],
                "medianCustomersAffected": row["ci_pt_est_sum"],
                "predictionLowerConfidenceBoundCustomersAffected": row["ci_lower_sum"],
                "weatherOutageCount": np.nan,
                "nonweatherOutageCount": np.nan,
                "weatherOutageCountRestOfDay": np.nan,
                "nonweatherOutageCountRestOfDay": np.nan,
                "submodelPredictions": [],
                "threatProbability": {},
            }
        }
    }

def process_hourly_zone_results(hourly_zone_df : pd.DataFrame) :
    logging.info("Processing hourly zone results df")
    documents = hourly_zone_df.apply(transform_hourly_zone_row, axis=1)
    write_hourly_zone_to_mongo(documents)

def write_hourly_zone_to_mongo(documents):
    generation_time = (
        datetime.now(tz=tz.UTC)
        .replace(minute=0, second=0, microsecond=0)
    )
    session_report = StormSessionBuilder(
        targetDate=generation_time,
        targetType= "hourly_zone",
        results= {}
    ).build()
    with session_report as s:
        for index, doc in documents.items():
            document_builder = HourlyDocumentBuilder(
                session=s.id,
                targetId=str(uuid.uuid4()),
                targetDate=doc["targetDate"],
                results=doc["results"],
                targetType="hourly_zone"
            )
            try:
                document_builder.build_and_save()
            except Exception as e:
                logging.error("Failed to save document.")
                logging.error(f"Error saving HOURLY_ZONE document: {e}")

####################
### Hourly System ##
####################
def get_hourly_system_df():
    hourly_system_query = f"""
    SELECT * 
    FROM {DBS_CATALOG}.{DBS_SCHEMA}.{DBS_HOURLY_SYSTEM_TABLE};
    """  # noqa

    try:
        logging.info(f"Getting hourly zone data from {DBS_CATALOG}.{DBS_SCHEMA}.{DBS_HOURLY_SYSTEM_TABLE} ...")
        df = run_dbs_query(cursor, hourly_system_query)

        if df.empty:
            logging.info(
                "The hourly system DataFrame is empty or not found. Skipping processing!"
            )
        else:
            process_hourly_system_results(df)

    except Exception as e:
        logging.error(f"Error running query: {e}")

def transform_hourly_system_row(row):
    return {
        "targetType": "hourly",
        "targetDate": row["utc_time"],
        "results": {
            "day": row["day"].strftime("%Y-%m-%d"),
            "hour": row["hour"],
            "hour_time": row["utc_time"],
            "generationTime": row["generation_time"],
            "generationTimeExact": row["generation_time_exact"],
            "location": "apc-system",
            "totalOutages": row["out_pt_est_sum"],
            "totalCustomersAffected": row["ci_pt_est_sum"],
            "weatherAgg": {
                "precip_inch_sum": row.get("hourly_precipitation_rate_inch_max"),
                "wind_mph_mean": row.get("wind_mph_mean"),
                "gust_mph_amax": row.get("gust_mph_max"),
                "temp_c_amin": row.get("temp_c_min"),
                "temp_c_mean": row.get("temp_c_mean"),
                "temp_c_amax": row.get("temp_c_max"),
                "temp_f_amin": row.get("temp_f_min"),
                "temp_f_mean": row.get("temp_f_mean"),
                "temp_f_amax": row.get("temp_f_max"),
                "humid_pct_mean": row.get("humid_pct_mean", np.nan),
                "spc_sow_amax": row.get("spc_sow_amax"),
                "spc_sow_amax_cat": row["spc_sow_amax_cat"],
                "cloud_cover_percent_mean": row.get("cloud_cover_percent_mean"),
                "wet_hourly_precipitation_rate_inch_max" : row.get("wet_hourly_precipitation_rate_inch_max"),
                "frozen_hourly_precipitation_rate_inch_max" : row.get("frozen_hourly_precipitation_rate_inch_max"),
                "mixed_hourly_precipitation_rate_inch_max" : row.get("mixed_hourly_precipitation_rate_inch_max")
            },
            "resultAgg": {
                "modelVersion": MODEL_VERSION,
                "predictionUpperConfidenceBound": row["out_lower_sum"],
                "medianOutageCount": row["out_pt_est_sum"],
                "predictionLowerConfidenceBound": row["out_upper_sum"],
                "predictionUpperConfidenceBoundCustomersAffected": row["ci_upper_sum"],
                "medianCustomersAffected": row["ci_pt_est_sum"],
                "predictionLowerConfidenceBoundCustomersAffected": row["ci_lower_sum"],
                "weatherOutageCount": np.nan,
                "nonweatherOutageCount": np.nan,
                "weatherOutageCountRestOfDay": np.nan,
                "nonweatherOutageCountRestOfDay": np.nan,
                "submodelPredictions": [],
                "threatProbability": {},
            }
        }
    }

def process_hourly_system_results(hourly_system_df : pd.DataFrame) :
    logging.info("Processing hourly zone results df")
    documents = hourly_system_df.apply(transform_hourly_system_row, axis=1)
    write_hourly_system_to_mongo(documents)

def write_hourly_system_to_mongo(documents):
    generation_time = (
        datetime.now(tz=tz.UTC)
        .replace(minute=0, second=0, microsecond=0)
    )
    session_report = StormSessionBuilder(
        targetDate=generation_time,
        targetType= "hourly",
        results= {}
    ).build()
    with session_report as s:
        for index, doc in documents.items():
            document_builder = HourlyDocumentBuilder(
                session=s.id,
                targetId=str(uuid.uuid4()),
                targetDate=doc["results"]["day"],
                results=doc["results"],
                targetType="hourly"
            )
            try:
                document_builder.build_and_save()
            except Exception as e:
                logging.error("Failed to save document.")
                logging.error(f"Error saving HOURLY document: {e}")

#########################
### Hourly Work Group ###
#########################
def get_hourly_work_group_df():
    hourly_work_group_query = f"""
    SELECT * 
    FROM {DBS_CATALOG}.{DBS_SCHEMA}.{DBS_HOURLY_WORK_GROUP_TABLE};
    """  # noqa

    try:
        logging.info(f"Getting hourly zone data from {DBS_CATALOG}.{DBS_SCHEMA}.{DBS_HOURLY_WORK_GROUP_TABLE} ...")
        df = run_dbs_query(cursor, hourly_work_group_query)

        if df.empty:
            logging.info(
                "The hourly Zone DataFrame is empty or not found. Skipping processing!"
            )
        else:
            process_hourly_work_group_results(df)

    except Exception as e:
        logging.error(f"Error running query: {e}")

def transform_hourly_work_group_row(row):
    return {
        "targetType": "hourly_work_group",
        "targetDate": row["utc_time"],
        "results": {
            "day": row["day"].strftime("%Y-%m-%d"),
            "hour": row["hour"],
            "hour_time": row["utc_time"],
            "generationTime": row["generation_time"],
            "generationTimeExact": row["generation_time_exact"],
            "location": "apc-zone",
            "totalOutages": row["out_pt_est_sum"],
            "totalCustomersAffected": row["ci_pt_est_sum"],
            "zone": row["zone"],
            "grouping": row["zone"],
            "displayZone": row["zone"].title(),  # TODO: process for edge cases
            "workGroup": row["work_group"],
            "weatherAgg": {
                "precip_inch_sum": row.get("hourly_precipitation_rate_inch_max"),
                "wind_mph_mean": row.get("wind_mph_mean"),
                "gust_mph_amax": row.get("gust_mph_max"),
                "temp_c_amin": row.get("temp_c_min"),
                "temp_c_mean": row.get("temp_c_mean"),
                "temp_c_amax": row.get("temp_c_max"),
                "temp_f_amin": row.get("temp_f_min"),
                "temp_f_mean": row.get("temp_f_mean"),
                "temp_f_amax": row.get("temp_f_max"),
                "humid_pct_mean": row.get("humid_pct_mean", np.nan),
                "spc_sow_amax": row.get("spc_sow_amax"),
                "spc_sow_amax_cat": row["spc_sow_amax_cat"],
                "cloud_cover_percent_mean": row.get("cloud_cover_percent_mean"),
                "wet_hourly_precipitation_rate_inch_max" : row.get("wet_hourly_precipitation_rate_inch_max"),
                "frozen_hourly_precipitation_rate_inch_max" : row.get("frozen_hourly_precipitation_rate_inch_max"),
                "mixed_hourly_precipitation_rate_inch_max" : row.get("mixed_hourly_precipitation_rate_inch_max")
            },
            "resultAgg": {
                "modelVersion": MODEL_VERSION,
                "predictionUpperConfidenceBound": row["out_lower_sum"],
                "medianOutageCount": row["out_pt_est_sum"],
                "predictionLowerConfidenceBound": row["out_upper_sum"],
                "predictionUpperConfidenceBoundCustomersAffected": row["ci_upper_sum"],
                "medianCustomersAffected": row["ci_pt_est_sum"],
                "predictionLowerConfidenceBoundCustomersAffected": row["ci_lower_sum"],
                "weatherOutageCount": np.nan,
                "nonweatherOutageCount": np.nan,
                "weatherOutageCountRestOfDay": np.nan,
                "nonweatherOutageCountRestOfDay": np.nan,
                "submodelPredictions": [],
                "threatProbability": {},
            }
        }
    }

def process_hourly_work_group_results(hourly_work_group_df : pd.DataFrame) :
    logging.info("Processing hourly work group results df")
    documents = hourly_work_group_df.apply(transform_hourly_work_group_row, axis=1)
    write_hourly_work_group_to_mongo(documents)

def write_hourly_work_group_to_mongo(documents):
    generation_time = (
        datetime.now(tz=tz.UTC)
        .replace(minute=0, second=0, microsecond=0)
    )
    session_report = StormSessionBuilder(
        targetDate=generation_time,
        targetType= "hourly_work_group",
        results= {}
    ).build()
    with session_report as s:
        for index, doc in documents.items():
            document_builder = HourlyDocumentBuilder(
                session=s.id,
                targetId=str(uuid.uuid4()),
                targetDate=doc["results"]["hour_time"],
                results=doc["results"],
                targetType="hourly_work_group"
            )
            try:
                document_builder.build_and_save()
            except Exception as e:
                logging.error("Failed to save document.")
                logging.error(f"Error saving HOURLY_WORK_GROUP document: {e}")

def run():
    while True:
        try:
            time.sleep(60)  # Simulate some work
        except Exception as e:
            logging.error(f"An error occurred: {e}")

## Interactive pod work ONLY! ##
# run()

##################
### Main block ###
##################

if CLIENT_AUTHORIZATION == "azure":
    try:
        access_token = retrieve_access_token()
        logging.info("Access token retrieved successfully.")
    except Exception as e:
        logging.error(f"Could not retrieve token: {e}")
        sys.exit(1)

    logging.info("Connecting to Azure Databricks...")
    conn = pyodbc.connect(dbs_conn_string_azure(access_token), autocommit=True)
    cursor = conn.cursor()
    logging.info("Databricks connection complete...")

    get_daily_system_df()
    get_daily_zone_df()
    get_daily_parent_zone_df()
    get_hourly_zone_df()
    get_hourly_system_df()
    #get_hourly_work_group_df() #not needed for now
    
    logging.info("Forecast writing complete!")
    
if CLIENT_AUTHORIZATION == "aws":
    logging.info("Connecting to Databricks in AWS...")

    try:
        with sql.connect(
            server_hostname=DBS_HOST,
            http_path=DBS_HTTP_PATH,
            access_token=AWS_ACCESS_TOKEN,
        ) as conn:
            logging.info("Connection to AWS Databricks successful!")

            with conn.cursor() as cursor:
                get_daily_system_df()
                get_daily_zone_df()
                try:
                    get_daily_parent_zone_df()
                except Exception as e:
                    logging.error(f"daily_parent_zone query error: Is daily parent zone set up for this account? Message: {e}")
                get_hourly_zone_df()
                get_hourly_system_df()
                #get_hourly_work_group_df() #not needed for now
                
                logging.info("Forecast writing complete!")

    except Exception as e:
        logging.error(f"Error connecting to AWS Databricks: {e}")

#run()
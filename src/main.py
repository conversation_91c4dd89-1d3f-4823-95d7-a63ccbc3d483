import json
import logging
import os
import sys
import time
import uuid
from datetime import datetime, timezone

import pandas as pd
import pyodbc
import requests
from databricks import sql
from pymongo import MongoClient, UpdateOne
from rabbitmq_writer.rabbitmq_client import RabbitMQClient

# --------------------
# ENVIRONMENT
# --------------------

# Logging Setup
logging.basicConfig(format="%(asctime)s %(levelname)s [%(name)s] %(message)s")
logging.getLogger().setLevel(os.environ.get("LOG_LEVEL", logging.INFO))

# Client Auth
CLIENT_AUTHORIZATION = os.getenv("CLIENT_AUTHORIZATION")

# Mongo Creds
MONGO_USER = os.getenv("MONGO_USER")
MONGO_PASSWORD = os.getenv("MONGO_PASSWORD")
MONGO_HOST = os.getenv("MONGO_HOST")

# Auth Control Flow
AWS_ACCESS_TOKEN = os.getenv("AWS_ACCESS_TOKEN")

# rabbitmq constants
RABBITMQ_EXCHANGE = os.getenv("RABBITMQ_EXCHANGE")
RABBITMQ_ROUTING_KEY = os.getenv("RABBITMQ_ROUTING_KEY")

# Databricks
DBS_HOST = os.getenv("DBS_HOST")
DBS_HTTP_PATH = os.getenv("DBS_HTTP_PATH")
DBS_PORT = os.getenv("DBS_PORT")

# Queries
DBS_CATALOG = os.getenv("DBS_CATALOG")
DBS_ACTUAL_SCHEMA = os.getenv("DBS_ACTUAL_SCHEMA", "apc_proj_storm")
DBS_ACTUAL_OUTAGES_TABLE = os.getenv("DBS_ACTUAL_OUTAGES_TABLE", "es_cleansed_daily_outages")

RESULT_DB_HOST = os.getenv("RESULT_DB_HOST")
RESULT_DB_PORT = int(os.getenv("RESULT_DB_PORT"))
RESULT_DB_USERNAME = os.getenv("RESULT_DB_USERNAME")
RESULT_DB_PASSWORD = os.getenv("RESULT_DB_PASSWORD")
RESULT_DB_DATABASE = os.getenv("RESULT_DB_DATABASE", "results")
RESULT_DB_COLLECTION = os.getenv("RESULT_DB_COLLECTION", "active_outages")

BACKPOPULATE = os.getenv("BACKPOPULATE", "false").lower() == "true"
BACKPOP_EARLIEST_DATE = os.getenv('BACKPOP_EARLIEST_DATE')
DAYS_BACK = int(os.getenv("DAYS_BACK", "31"))

# Auth Control Flow
# --------------------
# SERVICE PRINCIPAL
# --------------------
ACF_TENANT_ID = os.getenv("ACF_TENANT_ID")  # ap-ramp-XX-svcpri-etl.tenant
ACF_CLIENT_ID = os.getenv("ACF_CLIENT_ID")  # ap-ramp-XX-svcpri-etl.appId
ACF_CLIENT_SECRET = os.getenv("ACF_CLIENT_SECRET")  # ap-ramp-XX-svcpri-etl.password
ACF_SCOPE = os.getenv("ACF_SCOPE")  # DBX Service Scope

# --------------------
# Classes
# --------------------
class TokenRetrievalFailure(Exception):
    pass

# --------------------
# Functions
# --------------------
def retrieve_access_token() -> str:
    url = f"https://login.microsoftonline.com/{ACF_TENANT_ID}/oauth2/v2.0/token"
    data = {
        "client_id": ACF_CLIENT_ID,
        "client_secret": ACF_CLIENT_SECRET,
        "grant_type": "client_credentials",
        "scope": ACF_SCOPE,
    }
    headers = {"Content-Type": "application/x-www-form-urlencoded"}

    try:
        response = requests.post(url, data=data, headers=headers)
        response.raise_for_status()
        content = response.json()
        return content["access_token"]
    except requests.exceptions.RequestException as e:
        logging.error(f"Network error occurred: {e}")
        sys.exit(1)
    except (TokenRetrievalFailure, ValueError, KeyError) as e:
        logging.error(f"Error retrieving access token: {e}")
        sys.exit(1)


def dbs_conn_string_azure(access_token: str) -> str:
    return (
        f"host={DBS_HOST};"
        + f"port={DBS_PORT};"
        + f"httppath={DBS_HTTP_PATH};"
        + f"auth_accesstoken={access_token};"
        + "thrifttransport=2;"
        + "ssl=1;"
        + "authmech=11;"
        + "auth_flow=0;"
        + "driver=/opt/simba/spark/lib/64/libsparkodbc_sb64.so;"
    )

def df_size(df: pd.DataFrame):
    return df.shape[0]

def run_dbs_query(cursor, query: str):
    df = pd.DataFrame()

    try:
        logging.info(f"Running dbx query: '{query}'...")
        cursor.execute(query)
        df = pd.DataFrame.from_records(
            cursor.fetchall(), columns=[col[0] for col in cursor.description]
        )

        if not df.empty:
            logging.info(f"Query returned {df_size(df)} record(s).")
        else:
            logging.warning("Query returned no results.")

    except Exception as e:
        logging.error(e)

    return df

######################
### Actual Outages ###
######################
def get_daily_actual_outages_df():
    actual_outages_query = f"""
    SELECT *
    FROM {DBS_CATALOG}.{DBS_ACTUAL_SCHEMA}.{DBS_ACTUAL_OUTAGES_TABLE}
    WHERE date >= DATE_SUB(CURRENT_DATE(), {DAYS_BACK})
    ORDER BY date DESC
    """  # noqa

    try:
        logging.info(f"Getting actual outages data from {DBS_CATALOG}.{DBS_ACTUAL_SCHEMA}.{DBS_ACTUAL_OUTAGES_TABLE} ...")
        df = run_dbs_query(cursor, actual_outages_query)

        if df.empty:
            logging.info(
                "The actual Outages DataFrame is empty or not found. Skipping processing!"
            )
        else:
            logging.info(df)
            write_actual_outages(df)

    except Exception as e:
        logging.error(f"Error running query: {e}")

def get_backpop_actual_outages_df():
    
    if BACKPOP_EARLIEST_DATE:
        where_clause = f"WHERE date >= '{BACKPOP_EARLIEST_DATE}'"
    else:
        where_clause = ""

    backpop_actual_outages_query = f"""
    SELECT * 
    FROM {DBS_CATALOG}.{DBS_ACTUAL_SCHEMA}.{DBS_ACTUAL_OUTAGES_TABLE}
    {where_clause}
    ORDER BY date DESC;
    """  # noqa

    try:
        logging.info(f"Getting backpopulation actual outages data from {DBS_CATALOG}.{DBS_ACTUAL_SCHEMA}.{DBS_ACTUAL_OUTAGES_TABLE} ...")
        df = run_dbs_query(cursor, backpop_actual_outages_query)

        if df.empty:
            logging.info(
                "The Actual Outages DataFrame is empty or not found. Skipping processing!"
            )
        else:
            logging.info(df)
            write_actual_outages(df)

    except Exception as e:
        logging.error(f"Error running query: {e}")

def to_display_name(region):
    region = region.lower()
    region = region.replace('-', ' ')
    region = ' '.join(word.capitalize() for word in region.split())
    return region

def generate_target_id():
    """Generates a unique targetId starting with 'StormSession-'."""
    return f"StormSession-{uuid.uuid4()}"

# Port over to writer when approach is clean. Lambda/cron?
def write_actual_outages(df):
    client = MongoClient(
        host=RESULT_DB_HOST,
        port=RESULT_DB_PORT,
        username=RESULT_DB_USERNAME,
        password=RESULT_DB_PASSWORD,
        authSource="admin",
        authMechanism="SCRAM-SHA-256",
    )
    db = client[RESULT_DB_DATABASE]
    collection = db[RESULT_DB_COLLECTION]
    session_collection = db['result_session']
    
    df['date'] = pd.to_datetime(df['date'])
    df['last_updated'] = pd.to_datetime(df['last_updated'])
    
    # Generate session doc
    target_id = generate_target_id()
    execution_date = datetime.now()
    latest_date = df['date'].max()
    
    session_document = {
        "targetType": "active_outages",
        "targetId": target_id,
        "targetDate": latest_date,
        "results": {},
        "executionDate": execution_date,
        "completionDate": execution_date
    }
    
    session_result = session_collection.insert_one(session_document)
    session_id = session_result.inserted_id
    
    # These 3 will remove the old data so there's no duplicates.
    target_dates = df['date'].unique()
    target_dates_py = [date.to_pydatetime() for date in target_dates]
    collection.delete_many({"targetDate": {"$in": target_dates_py}})
    
    operations = []
    for date, group in df.groupby('date'):
        outage_records = []
        
        last_updated_max = group['last_updated'].max()
        timestamp = date

        for _, row in group.iterrows():
            outage_records.append({
                "region": to_display_name(row['region']),
                "activeCustomerOutages": 0,  # outages zero for now as data is not given
                "activeIncidents": row['incident_count']
            })
        
        # Create the document for outage records
        document = {
            "targetDate": timestamp,
            "createdTimestamp": timestamp,  # Use the actual current timestamp of write later, for now use the date of day data
            "outageRecords": outage_records,
            "lastUpdated": last_updated_max,
            "generatedBy": "System",
            "session": session_id
        }
        operations.append(
            UpdateOne(
                {"targetDate": timestamp},
                {"$set": document},
                upsert=True
            )
        )

    # Execute bulk operations (for backpop situation)
    if operations:
        result = collection.bulk_write(operations)
        print(f"Matched {result.matched_count}, Inserted {result.upserted_count}, Modified {result.modified_count}")
    
    logging.info("Writing Outages complete!")
    client.close()

    if target_dates:
        publish_active_outages_event(df['date'].min(), df['date'].max())

def run_backpopulation_or_query():
    if BACKPOPULATE:
        logging.info("Running backpopulation of actual outages data...")
        get_backpop_actual_outages_df()
    else:
        logging.info("Running Actual Outages Data Query...")
        get_daily_actual_outages_df()

def run():
    while True:
        try:
            time.sleep(60)
        except Exception as e:
            logging.error(f"An error occurred: {e}")

def publish_active_outages_event(min_date, max_date):
    client = RabbitMQClient()
    client.send_message(
        json.dumps(build_active_outages_event(min_date, max_date)),
        routing_key=RABBITMQ_ROUTING_KEY,
        exchange=RABBITMQ_EXCHANGE,
    )
    client.close_connection()

def build_active_outages_event(min_date, max_date):
    aggregate_overview_event = {
        "eventName": "activeOutagesEvent",
        "timestamp": to_rfc3339_string(datetime.utcnow()),  # pass in current time here
        "metadata": {
            "minDate": to_rfc3339_string(min_date),
            "maxDate": to_rfc3339_string(max_date),
        },
    }

    return aggregate_overview_event

def to_rfc3339_string(dt):
    if dt.tzinfo is None:
        dt = dt.replace(tzinfo=timezone.utc)

    return dt.isoformat()


## Interactive pod work ONLY! ##
#run()

##################
### Main block ###
##################
if CLIENT_AUTHORIZATION == "azure":
    try:
        access_token = retrieve_access_token()
        logging.info("Access token retrieved successfully.")
    except Exception as e:
        logging.error(f"Could not retrieve token: {e}")
        sys.exit(1)

    logging.info("Connecting to Databricks...")
    conn = pyodbc.connect(dbs_conn_string_azure(access_token), autocommit=True)
    cursor = conn.cursor()
    logging.info("Databricks connection complete...")

    run_backpopulation_or_query()

if CLIENT_AUTHORIZATION == "aws":
    logging.info("Connecting to Databricks in AWS...")

    try:
        with sql.connect(
            server_hostname=DBS_HOST,
            http_path=DBS_HTTP_PATH,
            access_token=AWS_ACCESS_TOKEN,
        ) as conn:
            logging.info("Connection to AWS Databricks successful!")

            with conn.cursor() as cursor:
                
                run_backpopulation_or_query()
                
                logging.info("Job Complete!")

    except Exception as e:
        logging.error(f"Error connecting to AWS Databricks: {e}")
from datetime import datetime

import numpy as np
import pandas as pd
import re

def predict(
    df: pd.DataFrame, 
    hr: datetime,
    current_time: datetime,
    glm_params_d,
    worker_counts: pd.DataFrame,
    latest_active: pd.DataFrame,
    ListCountTotalWorker: list
):

    # get current, prev hour
    hr_i_mask   = (df['Datetime_hour'] == hr)
    hr_i_1_mask = (df['Datetime_hour'] == (hr - pd.Timedelta(hours=1)))
    prev_hr     = df.loc[hr_i_1_mask]

    ColActiveOut = [('ao_' + re.sub('\.', '_', str(b))) for (b) in ListCountTotalWorker]
    ColRestoreOut = [('ro_' + re.sub('\.', '_', str(b))) for (b) in ListCountTotalWorker]
    
    if hr == current_time:
        # if current hour, use live active outage counts
        cao = [latest_active['activeIncidents'].values]*len(ListCountTotalWorker)
        df.loc[hr_i_mask, ColActiveOut] = list(map(list, zip(*cao)))

        # predict restored outages
        pred_df = predict_outages(
            df, 
            hr_i_mask, 
            df.loc[hr_i_mask], 
            glm_params_d, 
            worker_counts,
            ListCountTotalWorker
        )

        df.loc[hr_i_mask, ColRestoreOut] = pred_df.values
    
    else:
        # predict restored outages
        pred_df = predict_outages(
            df, 
            hr_i_mask, 
            prev_hr, 
            glm_params_d, 
            worker_counts,
            ListCountTotalWorker
        )

        df.loc[hr_i_mask, ColRestoreOut] = pred_df.values

        # active[hr i] = active[i-1] - restored[i]
        df.loc[hr_i_mask, ColActiveOut] = (prev_hr[ColActiveOut].values - df.loc[hr_i_mask, ColRestoreOut].values).clip(0)

def generate_simulation_data(
    data: pd.DataFrame,
    x_1 : pd.DataFrame,
    worker_counts: pd.DataFrame,
    ListCountTotalWorker: list
):
    for tcount_type in ListCountTotalWorker:
        ColActiveOut = 'ao_' + re.sub('\.', '_', str(tcount_type))
        # set active outage predict val
        data['active_outages_b1'] = (x_1[ColActiveOut].values).astype(int)
        data['percent_remaining'] = data['active_outages_b1'] / (data['new_outages_roll'] + 1)
        data['percent_remaining'] = data['percent_remaining'].fillna(0)
        data['percent_remaining'] = np.where(data.percent_remaining > 1,1, data.percent_remaining)
        data['total_workers']     = data[tcount_type].values
        data['percent_remaining_cut_lt20pct'] = data['percent_remaining'] <= 0.2
        data['percent_remaining_cut_gt90pct'] = data['percent_remaining'] >= 0.9
        data['territoryName'] = data['territoryName'].str.upper()
        data['Intercept'] = 1
        data['sin_time_cos_time'] = data['sin_time'] * data['cos_time']
        data['pred_fte'] = 0
        data['pred'] = 0

        yield data, tcount_type

def simulation_predict(
    data_generator,
    glm_params_d,
    ListCountTotalWorker
    ):
    pred_dict = {i:{
        'preds': predict_pipeline(d, glm_params_d), 
        'prefix': i} 
        for i, (d, c) in enumerate(data_generator)}

    pred_df = pd.DataFrame({x["prefix"]: x['preds'] for x in pred_dict.values()})
    predCol = [('ro_' + re.sub('\.', '_', str(b))) for (b) in ListCountTotalWorker]
    pred_df.columns = predCol

    return pred_df

def predict_pipeline(
    d,
    glm_params_d
):
    d = pd.concat([d, pd.get_dummies(d['territoryName'])], axis=1)
    pred = 0

    for key, value in glm_params_d.items():

        pred = pred + d[key]*value

    d['pred_fte'] = np.exp(pred.astype(float))
    d['pred_fte'] = d['pred_fte'].clip(0)
    d['pred']     = d['pred_fte'] * d['total_workers']

    return d['pred'].values

def predict_outages(
    df, hr_i_mask, x_1, glm_params_d, worker_counts, ListCountTotalWorker
):
    data = df.loc[hr_i_mask].copy()
    data_generator = generate_simulation_data(data, x_1,worker_counts,ListCountTotalWorker )
    pred_df = simulation_predict(data_generator, glm_params_d, ListCountTotalWorker)

    return pred_df

def severe_weather_event_etr(
    etrResults,
    df,
    current_time,
    ListCountTotalWorker 

):
    restored_regions, regions_to_search = get_regions_to_search_and_remove(etrResults, df, current_time)

    for area in restored_regions:

        mask = etrResults['territoryName'] == area
        for tcount_type in ListCountTotalWorker:
            ColActiveOut = 'ao_' + re.sub('\.', '_', str(tcount_type))
            ColEtrProj   = 'etr_proj_' + re.sub('\.', '_', str(tcount_type))
            ColEtrHours  = 'etr_hours_'+ re.sub('\.', '_', str(tcount_type))

            etrResults.loc[mask, ColEtrProj] = etrResults[mask]['etr_proj_received'].values[0]
            etrResults.loc[mask, ColEtrHours] = 0
                                  
    for area in regions_to_search:

        sc_df = df.loc[(df['territoryName'] == area) & (df['Datetime_hour'] >= current_time)]

        for tcount_type in ListCountTotalWorker:
            ColActiveOut = 'ao_' + re.sub('\.', '_', str(tcount_type))
            ColEtrProj   = 'etr_proj_' + re.sub('\.', '_', str(tcount_type))
            ColEtrHours  = 'etr_hours_'+ re.sub('\.', '_', str(tcount_type))

            # check for end conditions
            # has to be 6+hr after start time                
            sc_etr_conditions_df = sc_df[
                ((sc_df['Datetime_hour']) >= (etrResults['starttime'].unique()[0] + pd.Timedelta(hours=6))) & \
                (sc_df[ColActiveOut] <= sc_df['thresholdIncidents']/2)]
            
            # select first hour with etr conditions
            if len(sc_etr_conditions_df) > 0:
                etrResults.loc[etrResults['territoryName'] == area, ColEtrProj] = sc_etr_conditions_df['Datetime_hour'].min()
                etrResults.loc[etrResults['territoryName'] == area, ColEtrHours] = (sc_etr_conditions_df['Datetime_hour'].min() - etrResults['starttime'].unique()[0]) / np.timedelta64(1, 'h')

            else:
                etrResults.loc[etrResults['territoryName'] == area, ColEtrProj] = df.Datetime_hour.max()
                etrResults.loc[etrResults['territoryName'] == area, ColEtrHours] = (df.Datetime_hour.max() - etrResults['starttime'].unique()[0]) / np.timedelta64(1, 'h')

    return etrResults


def get_regions_to_search_and_remove(    
        etrResults,
        df,
        current_time,
    ):

    restored_regions = []
    regions_to_search = []
    regions = df.territoryName.unique()

    # if region med etr is <= current hour, check that current active outages <= 2
    restored_regions_past = etrResults[(
        (pd.to_datetime(etrResults['etr_proj_received']) < current_time)
        )]['territoryName'].unique()
    
    # if region med etr is == current hour, check that current active outages <= 2
    etr_is_now = etrResults[(
        (pd.to_datetime(etrResults['etr_proj_received']) == current_time)
        )]['territoryName'].unique()
    
    below_threshold_now = df[(
        (df['Datetime_hour'] == current_time)&
        (df['ao_tw_current'] <= df['thresholdIncidents']/2))]['territoryName'].unique()
    
    restored_regions_current = list(set(list(etr_is_now) + list(restored_regions_past)).intersection(below_threshold_now))

    if len(restored_regions_current) != 0:
        restored_regions = restored_regions_current
        regions_to_search =  [sc for sc in regions if sc not in restored_regions]

    else:
        regions_to_search =  [sc for sc in regions]
        
    return restored_regions, regions_to_search

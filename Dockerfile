FROM python:3.11-slim-bookworm

ARG NRU=storm-etl
ARG NRUG=docker-users
ARG CODE_ARTIFACT_TOKEN

ENV HOME=/home/<USER>
ENV VIRTUAL_ENV=$HOME/venv
ENV PATH=$VIRTUAL_ENV/bin:$PATH

ARG SED_FILE_PARSE_REGEX='/^\s*#/d;/^\s*$/d;s/[\r\n]//g'

ARG BUILD_CONFIG_DIR=config
ARG BUILD_SOURCE_DIR=src

WORKDIR /root

# Install OS packages
# --------------------------------------------------
COPY $BUILD_CONFIG_DIR/packages-os.conf packages-os.conf

RUN apt-get update && apt-get install -y curl unzip && apt-get clean && rm -rf /var/lib/apt/lists/*

# Install AWS CLI
RUN curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip" && \
  unzip awscliv2.zip && ./aws/install && rm -rf aws awscliv2.zip

# Includes security updates before the apt-get
RUN echo "deb http://security.debian.org/debian-security bookworm-security main" >> /etc/apt/sources.list

# Install additional OS packages and clean up
RUN apt-get update && \
    apt-get upgrade -y && \
    apt-get install -y --no-install-recommends $(sed $SED_FILE_PARSE_REGEX packages-os.conf) \
    linux-image-amd64 \
    linux-headers-amd64 && \
    apt-get install -y libtasn1-6=4.19.0-2+deb12u1 && \
    apt-mark hold libtasn1-6 && \
    rm -rf /var/lib/apt/lists/*

# Install ODBC Simba Driver
RUN wget https://databricks-bi-artifacts.s3.us-east-2.amazonaws.com/simbaspark-drivers/odbc/2.6.26/SimbaSparkODBC-2.6.26.1045-Debian-64bit.zip \
  && unzip SimbaSparkODBC-2.6.26.1045-Debian-64bit.zip \
  && rm SimbaSparkODBC-2.6.26.1045-Debian-64bit.zip \
  && dpkg -i simbaspark_2.6.26.1045-2_amd64.deb \
  && rm simbaspark_2.6.26.1045-2_amd64.deb

COPY $BUILD_CONFIG_DIR/odbc.ini /etc/odbc.ini
RUN chmod 777 /etc/odbc.ini
COPY $BUILD_CONFIG_DIR/odbcinst.ini /etc/odbcinst.ini

# Configure non-root user and home directory
RUN groupadd -g 999 $NRUG && useradd -r -u 999 -g $NRUG $NRU
RUN mkdir -m 755 -p $HOME && chown $NRU:$NRUG $HOME

# Remove any globally installed setuptools
RUN rm -rf /usr/lib/python*/dist-packages/setuptools* && rm -rf /usr/local/lib/python*/dist-packages/setuptools*

# Upgrade pip and setuptools globally
RUN python -m pip install --upgrade pip setuptools==70.0.0

# Show global setuptools version to confirm upgrade
RUN python -m pip show setuptools

# Switch to non-root user and set working directory
USER $NRU
WORKDIR $HOME

# Configure python virtual environment and install packages
RUN python -m venv $VIRTUAL_ENV && $VIRTUAL_ENV/bin/pip install --upgrade pip setuptools==70.0.0

# Show virtual environment setuptools version to confirm upgrade
RUN $VIRTUAL_ENV/bin/pip show setuptools

# Copy and install Python dependencies from config
COPY --chown=$NRU:$NRUG $BUILD_CONFIG_DIR/packages-py.conf $BUILD_CONFIG_DIR/packages-py.conf

# Use AWS artifact for Python packages
RUN pip config set global.extra-index-url https://aws:$<EMAIL>/pypi/pypi/simple/

# Install Python dependencies (with --no-cache-dir to prevent using cached packages)
RUN pip install --no-cache-dir $(sed $SED_FILE_PARSE_REGEX $BUILD_CONFIG_DIR/packages-py.conf)

# Copy source code
COPY --chown=$NRU:$NRUG $BUILD_SOURCE_DIR/main.py $BUILD_SOURCE_DIR/main.py

# Run the script
CMD ["python", "src/main.py"]
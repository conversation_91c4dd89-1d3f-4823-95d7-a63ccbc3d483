# syntax=docker/dockerfile:1

FROM continuumio/miniconda3:latest

ENV DEBIAN_FRONTEND=noninteractive
ENV PATH /opt/conda/envs/storm-etr/bin:$PATH

# Basics
RUN <<EOF
# Delete old dependencies that causes CVE-2022-32221
apt-get purge -y --auto-remove git
# Must use bookworm-backports to remediate CVE-2022-32221 until debian 11 packages are safer
echo "deb http://deb.debian.org/debian bookworm-backports main" | tee /etc/apt/sources.list.d/backports.list
apt-get update -y
apt-get upgrade -y
apt-get install -y curl
apt-get update -y -t bookworm-backports
apt-get upgrade -y -t bookworm-backports
apt-get install -y -t bookworm-backports --no-install-recommends build-essential unzip libpq-dev python3-dev

# Remove build dependencies after installation to reduce image size and security surface
apt remove -y build-essential
apt autoremove -y
apt clean
apt autoclean
rm -rf /var/lib/apt/lists/*

## installing on base environment to avoid default security vulnerability
conda update -n base conda -y
conda install -n base conda-libmamba-solver
conda config --set solver libmamba

# Install AWS CLI
curl 'https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip' -o 'awscliv2.zip'
unzip awscliv2.zip 
./aws/install 
rm -r aws && rm awscliv2.zip

# CVE Remediation for unused test packages (e.g., Flask vulnerabilities)
for version in 3.9 3.10 3.11 3.12; do
    if [ -f "/opt/conda/lib/python$version/site-packages/tests/conda_env/support/requirements.txt" ]; then
        rm "/opt/conda/lib/python$version/site-packages/tests/conda_env/support/requirements.txt";
    fi;
done
EOF

COPY environment_storm_etr.yml .

RUN <<EOF
# Install the necessary Python packages and resolve CVEs related to pip packages
pip install --upgrade pip
pip install cryptography==44.0.1 setuptools==80.9.0 certifi==2024.07.04 wheel==0.38.1 urllib3==2.0.6 requests==2.32.4

# Create conda environment from the environment file
conda env create -f environment_storm_etr.yml
conda init
conda activate storm-etr
conda install pip

# Clean up conda cache to reduce image size
conda clean -afy

# Activate the conda environment on launch
echo "source activate storm-etr" > ~/.bashrc
EOF

# Token fetch & Custom esource package installs
RUN --mount=type=secret,id=CODEARTIFACT,dst=/run/secrets/codeartifact pip config set global.extra-index-url https://aws:"$(cat /run/secrets/codeartifact)"@esource-int-artifacts-411985166407.d.codeartifact.us-east-1.amazonaws.com/pypi/pypi/simple/ && \
    python -m pip install esource-aws-tools==0.1.0 esource-results-writer==1.9.1 esource-results-reader==0.1.6 esource-etr-writer==3.9.1 esource-etr-reader==1.6.2

# Copy Python source files
COPY *.py ./

# Set the default shell to activate the conda environment
SHELL ["conda", "run", "-n", "storm-etr", "/bin/bash", "-c"]
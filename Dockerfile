FROM python:3.11-slim-buster

ARG NRU=storm-etl
ARG NRUG=docker-users
ARG CODE_ARTIFACT_TOKEN

ENV HOME=/home/<USER>
ENV VIRTUAL_ENV=$HOME/venv
ENV PATH=$VIRTUAL_ENV/bin:$PATH

ARG SED_FILE_PARSE_REGEX='/^\s*#/d;/^\s*$/d;s/[\r\n]//g'

ARG BUILD_CONFIG_DIR=config
ARG BUILD_SOURCE_DIR=src

WORKDIR /root

# Install OS packages
# --------------------------------------------------
COPY $BUILD_CONFIG_DIR/packages-os.conf packages-os.conf

# Install required packages (curl, unzip, wget, dpkg)
RUN apt-get update && \
  apt-get install -y curl unzip wget dpkg && \
  apt-get clean && \
  rm -rf /var/lib/apt/lists/*

# Install unixODBC for pyodbc dependency
RUN apt-get update && apt-get install -y unixodbc unixodbc-dev

# Download, unzip, install, and clean up AWS CLI installation
RUN curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip" && \
  unzip awscliv2.zip && \
  ./aws/install && \
  rm -rf aws awscliv2.zip

# Ensure the package list is updated and all vulnerable packages are upgraded
RUN apt-get update && apt-get upgrade -y --no-install-recommends && apt-get dist-upgrade -y

# Manually update expat to ensure the latest version (fixes CVE-2024-45490, CVE-2024-45491)
RUN apt-get install -y --only-upgrade libexpat1

# Install packages individually to ensure each one can be installed correctly
RUN apt-get install -y --only-upgrade libcurl3-gnutls libcurl4 curl libkrb5-3 libgssapi-krb5-2 libk5crypto3 libkrb5support0

# Clean up the apt cache
RUN apt-get clean && rm -rf /var/lib/apt/lists/*

# Upgrade vulnerable Python packages
RUN python -m pip uninstall -y setuptools certifi \
  && python -m pip install setuptools==80.9.0 certifi==2024.07.04

# Install required dependency for Simba ODBC driver
RUN apt-get update && apt-get install -y libsasl2-modules-gssapi-mit

# Install ODBC Simba Driver
RUN wget https://databricks-bi-artifacts.s3.us-east-2.amazonaws.com/simbaspark-drivers/odbc/2.6.26/SimbaSparkODBC-2.6.26.1045-Debian-64bit.zip \
  && if [ -f SimbaSparkODBC-2.6.26.1045-Debian-64bit.zip ]; then echo "Download successful"; else echo "Download failed"; exit 1; fi \
  && unzip SimbaSparkODBC-2.6.26.1045-Debian-64bit.zip \
  && rm SimbaSparkODBC-2.6.26.1045-Debian-64bit.zip \
  && dpkg -i simbaspark_2.6.26.1045-2_amd64.deb \
  && rm simbaspark_2.6.26.1045-2_amd64.deb

COPY $BUILD_CONFIG_DIR/odbc.ini /etc/odbc.ini
RUN chmod 777 /etc/odbc.ini
COPY $BUILD_CONFIG_DIR/odbcinst.ini /etc/odbcinst.ini

# Configure non-root user, group, and home directory
RUN groupadd -g 999 $NRUG \
  && useradd -r -u 999 -g $NRUG $NRU

RUN mkdir -m 755 -p $HOME \
  && chown $NRU:$NRUG $HOME

# Create scripts directory with proper permissions
RUN mkdir -p /scripts && chown $NRU:$NRUG /scripts

# Switch to NRU and set working directory
USER $NRU
WORKDIR $HOME

# Configure python VENV with proper libraries
RUN python -m venv $VIRTUAL_ENV \
  && python -m pip install --upgrade pip
COPY --chown=$NRU:$NRUG $BUILD_CONFIG_DIR/packages-py.conf $BUILD_CONFIG_DIR/packages-py.conf

# Use AWS artifact for packages
RUN pip config set global.extra-index-url https://aws:$<EMAIL>/pypi/pypi/simple/

# Install Python packages
RUN pip install $(sed $SED_FILE_PARSE_REGEX $BUILD_CONFIG_DIR/packages-py.conf)

# Copy in source code
COPY --chown=$NRU:$NRUG $BUILD_SOURCE_DIR/main.py $BUILD_SOURCE_DIR/main.py

RUN mkdir -p /scripts && chown $NRU:$NRUG /scripts

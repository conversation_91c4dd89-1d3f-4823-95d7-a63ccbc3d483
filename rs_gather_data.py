from typing import Union

import pandas as pd
import logging
from ETRReader.Reader import <PERSON><PERSON><PERSON><PERSON>er, get_database_connection_info_from_environment
from ResultsReader.Reader import get_database_connection_info_from_environment

class ETRInputs:
    def __init__(self, dbname: str):
        database_info = get_database_connection_info_from_environment()
        self._reader = ETRReader(
            database_info["host"], 
            database_info["port"], 
            database_info["username"], 
            database_info["password"], 
            dbname=dbname
        )

    def get_default_resources(self, storm_id: str = None) -> pd.DataFrame:
        try:
            default_resources = self._reader.get_default_resources_v2(storm_id)
        except RuntimeError:
            default_resources = self._reader.get_default_resources_v2()

        expanded_resources = pd.concat(
            [default_resources, default_resources["resources"].apply(pd.Series)],
            axis=1
        )

        return expanded_resources.sort_values(by=["territoryName"], ascending=True)

    def get_current_resources(self, storm_id: str=None) -> Union[pd.DataFrame, None]:
        if storm_id is None:
            logging.info("No storm_id passed, reverting to default resources.")
        else:
            try:
                current_resources = self._reader.get_current_resources_v2(storm_id)
            except RuntimeError:
                current_resources = pd.DataFrame()
            if not current_resources.empty:
                return (
                    pd.concat(
                        [
                            current_resources,
                            current_resources["resources"].apply(pd.Series)
                        ],
                        axis=1
                    )
                )
            else:
                logging.warning(f"No current resources found for storm id: {storm_id}. Reverting to default resources")

        return self.get_default_resources(storm_id).sort_values(by=["territoryName"], ascending=True)

    def get_future_resources(self, storm_id: str=None) -> Union[pd.DataFrame, None]:
        if storm_id is None:
            logging.info("No storm_id passed.")
        else:
            future_resources = self._reader.get_future_resources_v2(storm_id)
            if not future_resources.empty:
                return (
                    pd.concat(
                        [
                            future_resources, 
                            future_resources["resources"].apply(pd.Series)
                        ],
                        axis=1
                    ).sort_values(by=["territoryName"], ascending=True)
                )
            else:
                logging.warning(f"No future resources found for storm id: {storm_id}")

        return None
    
    def get_latest_active_outages(
            self,
            territoryNames
        ) -> pd.DataFrame:
        
        active_outages =  (
            pd.DataFrame(
                self._reader.get_latest_active_outages()["outageRecords"][0]
            )
            .sort_values("region", ascending=True)
        )
        active_outages = ((
            active_outages[active_outages['region'].str.lower() != "system"]
            .reset_index(drop=True)
        )
        .rename(columns = {'region':'territoryName'}))
        
        active_outage_count_d = {territory : None for territory in territoryNames}
        active_outages_full = pd.DataFrame(active_outage_count_d.items(), columns=['territoryName', 'temp'])
        active_outages_full = (
            pd.merge(active_outages_full, active_outages, how = 'left', on ='territoryName')
            .fillna(0)
            .assign(
                activeIncidents = lambda x: (x.activeIncidents).astype(int),
                activeCustomerOutages = lambda x: (x.activeCustomerOutages).astype(int)
            )
            .drop(columns=['temp'])
        )
        return (
            active_outages_full
            .sort_values(by=["territoryName"], ascending=True)
        )
    
    def get_historical_outages(
            self,
            territoryNames
    ) -> pd.DataFrame:

        historical_outages = self._reader.get_historical_outages()
        
        if len(historical_outages) == 0:
            
            rolling_outage_count_d = {territory : 0 for territory in territoryNames}
            rolling_outage_count = pd.DataFrame(rolling_outage_count_d.items(), columns=['territoryName', 'new_outages_roll'])
        
        else:
            
            rolling_outage_raw = pd.concat([pd.DataFrame(row['outageRecords']) for _,row in historical_outages.iterrows()],ignore_index=True)
            rolling_outage_raw['new_outages_roll'] = rolling_outage_raw['incidentName']
            rolling_outage_raw['territoryName'] = rolling_outage_raw['region'].str.title()

            rolling_outage_count = rolling_outage_raw.groupby('territoryName').new_outages_roll.nunique().reset_index()

        return (
            rolling_outage_count
            .sort_values(by=["territoryName"], ascending=True)
        )
    
    def get_thresholds(self) -> pd.DataFrame:
        
        thresholds = self._reader.get_territory_storm_thresholds()[['territoryName','thresholdIncidents','thresholdOutages']]

        return (
            thresholds
            .sort_values(by=["territoryName"], ascending=True)
        )
    
    def get_open_storms(self):
        
        open_storms = self._reader.get_open_storms()

        if open_storms is not None:

            storm_entity = (
                open_storms
                .sort_values(by=["creationDate"])
                .reset_index(drop=True)
                .head(1)
            )

            storm_id     = str(storm_entity['_id'].unique()[0])
            mode         = storm_entity["stormMode"].unique()[0]
        
        else:
            storm_entity = None
            storm_id = None
            mode     = "off"

        return storm_entity, storm_id, mode

    def get_post_restoration_storms(self):

        return self._reader.get_storms_by_mode("post-restoration")

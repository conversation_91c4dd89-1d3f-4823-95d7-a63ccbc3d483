clone:
  depth: full
definitions:
  services:
    docker:
      image:
        name: docker:dind
        username: $DOCKER_HUB_USERNAME
        password: $DOCKER_HUB_PASSWORD
        email: $DOCKER_HUB_EMAIL
  steps:
    - step: &prep-env
        name: Prepare Environment Vars
        oidc: true
        image:
          name: amazon/aws-cli:latest
          username: $DOCKER_HUB_USERNAME
          password: $DOCKER_HUB_PASSWORD
          email: $DOCKER_HUB_EMAIL
        runs-on:
          - self.hosted
          - linux
        script:
          # Set AWSCLI env vars
          - export AWS_REGION=us-east-1
          - export AWS_ROLE_ARN=arn:aws:iam::************:role/product-iac
          - export AWS_WEB_IDENTITY_TOKEN_FILE=$(pwd)/web-identity-token
          - echo $BITBUCKET_STEP_OIDC_TOKEN > $(pwd)/web-identity-token
          # Create Canonical Version
          - echo export BASE_ECR_URL="************.dkr.ecr.us-east-1.amazonaws.com" >> build.env
          - export timestamp=$(date +%s | tr -d '[:space:]')
          - export escaped_branch=$(echo $BITBUCKET_BRANCH | sed "s=/=_=g" | tr -d '[:space:]')
          - echo export ESCAPED_BRANCH="${escaped_branch}" >> build.env
          - echo export BUILD_IMAGE_TAG="${escaped_branch}-${BITBUCKET_COMMIT:0:8}-${timestamp}" >> build.env
          # Get Snyk Creds
          - echo export SNYK_TOKEN=$(aws secretsmanager get-secret-value --secret-id devTools/snyk_token --query SecretString --output text) >> build.env
          # Get AWS Creds
          - echo export AWS_TOKEN=$(aws ecr get-login-password --region us-east-1) >> build.env
          # Get ACR Creds
          - echo export ACR_DV_TOKEN=$(aws secretsmanager get-secret-value --secret-id platform/acr_dv_token --query SecretString --output text) >> build.env
          - echo export ACR_DV_2_TOKEN=$(aws secretsmanager get-secret-value --secret-id platform/acr_dv_2_token --query SecretString --output text) >> build.env
          - echo export ACR_DV_2_TOKEN=$(aws secretsmanager get-secret-value --secret-id platform/acr_dv_2_token --query SecretString --output text) >> build.env
          - echo export ACR_UA_TOKEN=$(aws secretsmanager get-secret-value --secret-id platform/acr_ua_token --query SecretString --output text) >> build.env
          - echo export ACR_UA_2_TOKEN=$(aws secretsmanager get-secret-value --secret-id platform/acr_ua_2_token --query SecretString --output text) >> build.env
          - echo export ACR_UA_2_TOKEN=$(aws secretsmanager get-secret-value --secret-id platform/acr_ua_2_token --query SecretString --output text) >> build.env
          # Code Artifact Creds
          - echo export CODEARTIFACT_AUTH_TOKEN=$(aws codeartifact get-authorization-token --domain esource-int-artifacts --domain-owner ************ --region us-east-1 --query authorizationToken --output text) >> build.env
        after-script:
          - if [[ BITBUCKET_EXIT_CODE -ne 0 ]]; then echo "$DOCKER_HUB_PASSWORD" | docker login -u "$DOCKER_HUB_USERNAME" --password-stdin && docker run --rm esdsdockerserviceaccount/notify-teams:latest notify ${TEAMS_WEBHOOK} "Build Failed" "${BITBUCKET_REPO_FULL_NAME} - ${BITBUCKET_BRANCH}"; fi
        services:
          - docker
        artifacts:
          - build.env
    - step: &extract-setup-values
        name: Extract Values from Setup.py
        image:
          name: python:3.9-slim
          username: $DOCKER_HUB_USERNAME
          password: $DOCKER_HUB_PASSWORD
          email: $DOCKER_HUB_EMAIL
        runs-on:
          - self.hosted
          - linux
        script:
          - source build.env
          - echo export BUILD_IMAGE_ORG=esource >> build.env
          - echo export BUILD_IMAGE_NAME=$(python setup.py --name) >> build.env
        after-script:
          - if [[ BITBUCKET_EXIT_CODE -ne 0 ]]; then echo "$DOCKER_HUB_PASSWORD" | docker login -u "$DOCKER_HUB_USERNAME" --password-stdin && docker run --rm esdsdockerserviceaccount/notify-teams:latest notify ${TEAMS_WEBHOOK} "Build Failed" "${BITBUCKET_REPO_FULL_NAME} - ${BITBUCKET_BRANCH}"; fi
        services:
          - docker
        caches:
          - docker
        artifacts:
          - build.env
    - step: &sonarqube
        name: SonarQube Quality Gate
        image:
          name: sonarsource/sonar-scanner-cli:11.1
          username: $DOCKER_HUB_USERNAME
          password: $DOCKER_HUB_PASSWORD
          email: $DOCKER_HUB_EMAIL
        runs-on:
          - self.hosted
          - linux
        script:
          - source build.env
          - sonar-scanner
        after-script:
          - if [[ BITBUCKET_EXIT_CODE -ne 0 ]]; then echo "$DOCKER_HUB_PASSWORD" | docker login -u "$DOCKER_HUB_USERNAME" --password-stdin && docker run --rm esdsdockerserviceaccount/notify-teams:latest notify ${TEAMS_WEBHOOK} "Build Failed" "${BITBUCKET_REPO_FULL_NAME} - ${BITBUCKET_BRANCH}"; fi
        services:
          - docker
        caches:
          - docker
    - step: &snyk-code
        name: Synk - Code Test
        oidc: true
        runs-on:
          - self.hosted
          - linux
        image: 
          name: snyk/snyk:python-3.11
          username: $DOCKER_HUB_USERNAME
          password: $DOCKER_HUB_PASSWORD
          email: $DOCKER_HUB_EMAIL
        script:
          - source build.env
          - pip install --upgrade pip setuptools==80.9.0 pytest pytest-cov
          - python setup.py egg_info
          - pip install --no-cache-dir -r $(echo $BUILD_IMAGE_NAME | sed 's/-/_/g').egg-info/requires.txt
          - pip config set global.index-url https://aws:$<EMAIL>/pypi/pypi/simple/
          - pip install --no-cache-dir -r config/packages-py.conf
          #- pytest test/unit/ --cov=./src --cov-report=xml:cov.xml
          - snyk test --file=setup.py --fail-on=all --severity-threshold=high --color --json-file-output=output.json
        after-script:
          - if [[ BITBUCKET_EXIT_CODE -ne 0 ]]; then echo "$DOCKER_HUB_PASSWORD" | docker login -u "$DOCKER_HUB_USERNAME" --password-stdin && docker run --rm esdsdockerserviceaccount/notify-teams:latest notify ${TEAMS_WEBHOOK} "Build Failed" "${BITBUCKET_REPO_FULL_NAME} - ${BITBUCKET_BRANCH}"; fi
        services:
          - docker
        caches:
          - docker
        artifacts:
          - output.json
    - step: &upload-snyk-code-report
        name: Synk - Upload Code Report
        oidc: true
        runs-on:
          - self.hosted
          - linux
        image:
          name: bitbucketpipelines/bitbucket-upload-file:0.7.3
          username: $DOCKER_HUB_USERNAME
          password: $DOCKER_HUB_PASSWORD
          email: $DOCKER_HUB_EMAIL
        script:
          - source build.env
          - export BITBUCKET_ACCESS_TOKEN=$BITBUCKET_TOKEN
          - export FILENAME=snyk-code-$BUILD_IMAGE_TAG.json
          - cp output.json $FILENAME
          - python3 /pipe.py
        after-script:
          - if [[ BITBUCKET_EXIT_CODE -ne 0 ]]; then echo "$DOCKER_HUB_PASSWORD" | docker login -u "$DOCKER_HUB_USERNAME" --password-stdin && docker run --rm esdsdockerserviceaccount/notify-teams:latest notify ${TEAMS_WEBHOOK} "Build Failed" "${BITBUCKET_REPO_FULL_NAME} - ${BITBUCKET_BRANCH}"; fi
        services:
          - docker
        caches:
          - docker
    - step: &build-image
        name: Build Image
        oidc: true
        image:
          name: docker:20
          username: $DOCKER_HUB_USERNAME
          password: $DOCKER_HUB_PASSWORD
          email: $DOCKER_HUB_EMAIL
        runs-on:
          - self.hosted
          - linux
        script:
          - source build.env
          - docker build . -t $BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:$BUILD_IMAGE_TAG --build-arg CODE_ARTIFACT_TOKEN=$CODEARTIFACT_AUTH_TOKEN
          - docker save --output built.tar $BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:$BUILD_IMAGE_TAG
        after-script:
          - if [[ BITBUCKET_EXIT_CODE -ne 0 ]]; then echo "$DOCKER_HUB_PASSWORD" | docker login -u "$DOCKER_HUB_USERNAME" --password-stdin && docker run --rm esdsdockerserviceaccount/notify-teams:latest notify ${TEAMS_WEBHOOK} "Build Failed" "${BITBUCKET_REPO_FULL_NAME} - ${BITBUCKET_BRANCH}"; fi
        services:
          - docker
        caches:
          - docker
        artifacts:
          - built.tar
          - build.env
    - step: &snyk-image
        name: Synk - Image Test
        oidc: true
        runs-on:
          - self.hosted
          - linux
        image:
          name: snyk/snyk:docker
          username: $DOCKER_HUB_USERNAME
          password: $DOCKER_HUB_PASSWORD
          email: $DOCKER_HUB_EMAIL
        script:
          - source build.env
          - snyk container test docker-archive:built.tar --fail-on=all --severity-threshold=critical --print-deps --color --json-file-output=output.json
        after-script:
          - if [[ BITBUCKET_EXIT_CODE -ne 0 ]]; then echo "$DOCKER_HUB_PASSWORD" | docker login -u "$DOCKER_HUB_USERNAME" --password-stdin && docker run --rm esdsdockerserviceaccount/notify-teams:latest notify ${TEAMS_WEBHOOK} "Build Failed" "${BITBUCKET_REPO_FULL_NAME} - ${BITBUCKET_BRANCH}"; fi
        services:
          - docker
        artifacts:
          - output.json
    - step: &upload-snyk-image-report
        name: Synk - Upload Image Test Report
        oidc: true
        runs-on:
          - self.hosted
          - linux
        image:
          name: bitbucketpipelines/bitbucket-upload-file:0.7.3
          username: $DOCKER_HUB_USERNAME
          password: $DOCKER_HUB_PASSWORD
          email: $DOCKER_HUB_EMAIL
        script:
          - source build.env
          - export BITBUCKET_ACCESS_TOKEN=$BITBUCKET_TOKEN
          - export FILENAME=snyk-image-$BUILD_IMAGE_TAG.json
          - cp output.json $FILENAME
          - python3 /pipe.py
        after-script:
          - if [[ BITBUCKET_EXIT_CODE -ne 0 ]]; then echo "$DOCKER_HUB_PASSWORD" | docker login -u "$DOCKER_HUB_USERNAME" --password-stdin && docker run --rm esdsdockerserviceaccount/notify-teams:latest notify ${TEAMS_WEBHOOK} "Build Failed" "${BITBUCKET_REPO_FULL_NAME} - ${BITBUCKET_BRANCH}"; fi
        services:
          - docker
        caches:
          - docker
    - step: &push-ecr
        name: Push to ECR
        oidc: true
        runs-on:
          - self.hosted
          - linux
        image:
          name: amazon/aws-cli:latest
          username: $DOCKER_HUB_USERNAME
          password: $DOCKER_HUB_PASSWORD
          email: $DOCKER_HUB_EMAIL
        script:
          - source build.env
          - docker load --input built.tar
          - echo $AWS_TOKEN | docker login -u AWS --password-stdin $BASE_ECR_URL/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME
          - docker tag $BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:$BUILD_IMAGE_TAG $BASE_ECR_URL/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:$BUILD_IMAGE_TAG
          - docker push $BASE_ECR_URL/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:$BUILD_IMAGE_TAG
          - docker tag $BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:$BUILD_IMAGE_TAG $BASE_ECR_URL/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:$ESCAPED_BRANCH
          - docker push $BASE_ECR_URL/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:$ESCAPED_BRANCH
        after-script:
          - if [[ BITBUCKET_EXIT_CODE -ne 0 ]]; then echo "$DOCKER_HUB_PASSWORD" | docker login -u "$DOCKER_HUB_USERNAME" --password-stdin && docker run --rm esdsdockerserviceaccount/notify-teams:latest notify ${TEAMS_WEBHOOK} "Build Failed" "${BITBUCKET_REPO_FULL_NAME} - ${BITBUCKET_BRANCH}"; fi
        services:
          - docker
        caches:
          - docker
    - step: &push-acr-development
        name: Push to ACR - Development
        oidc: true
        runs-on:
          - self.hosted
          - linux
        script:
          - source build.env
          - docker load --input built.tar
          - export AZURE_REPO=aprampdvacr.azurecr.io
          - export REPO_USER=aprampdvacr
          - echo $ACR_DV_TOKEN | docker login -u $REPO_USER --password-stdin $AZURE_REPO
          - docker tag $BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:$BUILD_IMAGE_TAG $AZURE_REPO/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:$BUILD_IMAGE_TAG
          - docker push $AZURE_REPO/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:$BUILD_IMAGE_TAG
          - docker tag $AZURE_REPO/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:$BUILD_IMAGE_TAG $AZURE_REPO/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:latest
          - docker push $AZURE_REPO/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:latest
        after-script:
          - if [[ BITBUCKET_EXIT_CODE -ne 0 ]]; then echo "$DOCKER_HUB_PASSWORD" | docker login -u "$DOCKER_HUB_USERNAME" --password-stdin && docker run --rm esdsdockerserviceaccount/notify-teams:latest notify ${TEAMS_WEBHOOK} "Build Failed" "${BITBUCKET_REPO_FULL_NAME} - ${BITBUCKET_BRANCH}"; fi
        services:
          - docker
        caches:
          - docker
    - step: &push-acr-development-2
        name: Push to ACR - Development
        oidc: true
        runs-on:
          - self.hosted
          - linux
        script:
          - source build.env
          - docker load --input built.tar
          - export AZURE_REPO=scspeardvacr.azurecr.io
          - export REPO_USER=scspeardvacr
          - echo $ACR_DV_2_TOKEN | docker login -u $REPO_USER --password-stdin $AZURE_REPO
          - docker tag $BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:$BUILD_IMAGE_TAG $AZURE_REPO/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:$BUILD_IMAGE_TAG
          - docker push $AZURE_REPO/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:$BUILD_IMAGE_TAG
          - docker tag $AZURE_REPO/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:$BUILD_IMAGE_TAG $AZURE_REPO/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:latest
          - docker push $AZURE_REPO/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:latest
        after-script:
          - if [[ BITBUCKET_EXIT_CODE -ne 0 ]]; then echo "$DOCKER_HUB_PASSWORD" | docker login -u "$DOCKER_HUB_USERNAME" --password-stdin && docker run --rm esdsdockerserviceaccount/notify-teams:latest notify ${TEAMS_WEBHOOK} "Build Failed" "${BITBUCKET_REPO_FULL_NAME} - ${BITBUCKET_BRANCH}"; fi
        services:
          - docker
    - step: &push-acr-development-2
        name: Push to ACR - Development
        oidc: true
        runs-on:
          - self.hosted
          - linux
        script:
          - source build.env
          - docker load --input built.tar
          - export AZURE_REPO=scspeardvacr.azurecr.io
          - export REPO_USER=scspeardvacr
          - echo $ACR_DV_2_TOKEN | docker login -u $REPO_USER --password-stdin $AZURE_REPO
          - docker tag $BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:$BUILD_IMAGE_TAG $AZURE_REPO/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:$BUILD_IMAGE_TAG
          - docker push $AZURE_REPO/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:$BUILD_IMAGE_TAG
          - docker tag $AZURE_REPO/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:$BUILD_IMAGE_TAG $AZURE_REPO/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:latest
          - docker push $AZURE_REPO/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:latest
        after-script:
          - if [[ BITBUCKET_EXIT_CODE -ne 0 ]]; then echo "$DOCKER_HUB_PASSWORD" | docker login -u "$DOCKER_HUB_USERNAME" --password-stdin && docker run --rm esdsdockerserviceaccount/notify-teams:latest notify ${TEAMS_WEBHOOK} "Build Failed" "${BITBUCKET_REPO_FULL_NAME} - ${BITBUCKET_BRANCH}"; fi
        services:
          - docker
    - step: &push-acr-staging
        name: Push to ACR - Staging
        oidc: true
        runs-on:
          - self.hosted
          - linux
        script:
          - source build.env
          - docker load --input built.tar
          - export AZURE_REPO=aprampuaacr.azurecr.io
          - export REPO_USER=aprampuaacr
          - echo $ACR_UA_TOKEN | docker login -u $REPO_USER --password-stdin $AZURE_REPO
          - docker tag $BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:$BUILD_IMAGE_TAG $AZURE_REPO/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:$BUILD_IMAGE_TAG
          - docker push $AZURE_REPO/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:$BUILD_IMAGE_TAG
        after-script:
          - if [[ BITBUCKET_EXIT_CODE -ne 0 ]]; then echo "$DOCKER_HUB_PASSWORD" | docker login -u "$DOCKER_HUB_USERNAME" --password-stdin && docker run --rm esdsdockerserviceaccount/notify-teams:latest notify ${TEAMS_WEBHOOK} "Build Failed" "${BITBUCKET_REPO_FULL_NAME} - ${BITBUCKET_BRANCH}"; fi
        services:
          - docker
    - step: &push-acr-staging-2
        name: Push to ACR - Staging
        oidc: true
        runs-on:
          - self.hosted
          - linux
        script:
          - source build.env
          - docker load --input built.tar
          - export AZURE_REPO=scspearuaacr.azurecr.io
          - export REPO_USER=scspearuaacr
          - echo $ACR_UA_2_TOKEN | docker login -u $REPO_USER --password-stdin $AZURE_REPO
          - docker tag $BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:$BUILD_IMAGE_TAG $AZURE_REPO/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:$BUILD_IMAGE_TAG
          - docker push $AZURE_REPO/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:$BUILD_IMAGE_TAG
        after-script:
          - if [[ BITBUCKET_EXIT_CODE -ne 0 ]]; then echo "$DOCKER_HUB_PASSWORD" | docker login -u "$DOCKER_HUB_USERNAME" --password-stdin && docker run --rm esdsdockerserviceaccount/notify-teams:latest notify ${TEAMS_WEBHOOK} "Build Failed" "${BITBUCKET_REPO_FULL_NAME} - ${BITBUCKET_BRANCH}"; fi
        services:
          - docker
        caches:
          - docker
        caches:
          - docker
    - step: &push-acr-staging-2
        name: Push to ACR - Staging
        oidc: true
        runs-on:
          - self.hosted
          - linux
        script:
          - source build.env
          - docker load --input built.tar
          - export AZURE_REPO=scspearuaacr.azurecr.io
          - export REPO_USER=scspearuaacr
          - echo $ACR_UA_2_TOKEN | docker login -u $REPO_USER --password-stdin $AZURE_REPO
          - docker tag $BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:$BUILD_IMAGE_TAG $AZURE_REPO/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:$BUILD_IMAGE_TAG
          - docker push $AZURE_REPO/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:$BUILD_IMAGE_TAG
        after-script:
          - if [[ BITBUCKET_EXIT_CODE -ne 0 ]]; then echo "$DOCKER_HUB_PASSWORD" | docker login -u "$DOCKER_HUB_USERNAME" --password-stdin && docker run --rm esdsdockerserviceaccount/notify-teams:latest notify ${TEAMS_WEBHOOK} "Build Failed" "${BITBUCKET_REPO_FULL_NAME} - ${BITBUCKET_BRANCH}"; fi
        services:
          - docker
        caches:
          - docker
    - step: &apc-retag
        name: Re-tag existing image and push to ACR Staging
        oidc: true
        runs-on: 
          - self.hosted
          - linux
        script:
          - source build.env
          - echo $AWS_TOKEN | docker login -u AWS --password-stdin $BASE_ECR_URL/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME
          - echo $BASE_ECR_URL/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:$EXISTING_TAG
          - docker pull $BASE_ECR_URL/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:$EXISTING_TAG
          - export AZURE_REPO=aprampuaacr.azurecr.io
          - export REPO_USER=aprampuaacr
          - export AZURE_REPO_2=scspearuaacr.azurecr.io
          - export REPO_USER_2=scspearuaacr
          - echo $ACR_UA_TOKEN | docker login -u $REPO_USER --password-stdin $AZURE_REPO
          - docker tag $BASE_ECR_URL/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:$EXISTING_TAG $AZURE_REPO/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:latest
          - docker push $AZURE_REPO/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:latest  
          - echo $ACR_UA_2_TOKEN | docker login -u $REPO_USER_2 --password-stdin $AZURE_REPO_2
          - docker tag $BASE_ECR_URL/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:$EXISTING_TAG $AZURE_REPO_2/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:latest
          - docker push $AZURE_REPO_2/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:latest
        after-script:
          - if [[ BITBUCKET_EXIT_CODE -ne 0 ]]; then echo "$DOCKER_HUB_PASSWORD" | docker login -u "$DOCKER_HUB_USERNAME" --password-stdin && docker run --rm esdsdockerserviceaccount/notify-teams:latest notify ${TEAMS_WEBHOOK} "Build Failed" "${BITBUCKET_REPO_FULL_NAME} - ${BITBUCKET_BRANCH}"; fi
        services:
          - docker
        caches:
          - docker
    - step: &finish
        name: Notify Teams of Success
        oidc: true
        runs-on: 
          - self.hosted
          - linux
        script:
          - source build.env
          - echo "$DOCKER_HUB_PASSWORD" | docker login -u "$DOCKER_HUB_USERNAME" --password-stdin
          - docker run --rm esdsdockerserviceaccount/notify-teams:latest notify ${TEAMS_WEBHOOK} "Build Succeeded" "${BITBUCKET_REPO_FULL_NAME} -- ${BITBUCKET_BRANCH} -- $BASE_ECR_URL/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:$BUILD_IMAGE_TAG"
        services:
          - docker
        caches:
          - docker

pipelines:
  custom:
    apc-retag-for-staging:
      - variables:
          - name: EXISTING_TAG
            description: "Image tag that will be retagged as 'latest' and pushed to APC staging environment"
      - step: *prep-env
      - step: *extract-setup-values
      - step: *apc-retag
  default:
    - step: *prep-env
    - step: *extract-setup-values
    - step: *sonarqube
    - step: *snyk-code
    - step: *upload-snyk-code-report
    - step: *finish
  branches:
    '{development}':
      - step: *prep-env
      - step: *extract-setup-values
      - step: *sonarqube
      - step: *snyk-code
      - step: *upload-snyk-code-report
      - step: *build-image
      - step: *snyk-image
      - step: *upload-snyk-image-report
      - step: *push-ecr
      - step: *push-acr-development
      - step: *push-acr-development-2
      - step: *finish
    '{staging}':
      - step: *prep-env
      - step: *extract-setup-values
      - step: *sonarqube
      - step: *snyk-code
      - step: *upload-snyk-code-report
      - step: *build-image
      - step: *snyk-image
      - step: *upload-snyk-image-report
      - step: *push-ecr
      - step: *push-acr-staging
      - step: *push-acr-staging-2
      - step: *finish
    '{main}':
      - step: *prep-env
      - step: *extract-setup-values
      - step: *sonarqube
      - step: *snyk-code
      - step: *upload-snyk-code-report
      - step: *build-image
      - step: *snyk-image
      - step: *upload-snyk-image-report
      - step: *push-ecr
      - step: *finish

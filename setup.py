from setuptools import find_packages, setup

setup(
    name="storm-forecasting-databricks-ingest",
    version="0.1",
    description="Project for ingesting live forecasting weather data from databricks to mongo.",
    author="E Source Data Science",
    author_email="<EMAIL>",
    packages=find_packages(
        exclude=[
            "test",
            "test*",
            "Dockerfile",
            "Jenkinsfile",
            ".gitignore",
        ]
    ),
    install_requires=[
        "boto3",
        "pandas==1.5.2",
        "s3fs",
        "pyspark",
        "databricks"
    ],
    python_requires=">=3.9",
)

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import re

from rs_utils import convert_timestamp_from_utc_to, safe_column

def generate_simmode_counts(
    resources_simmode: pd.DataFrame
):
    territory_totals = resources_simmode.groupby('territoryName').sum().assign(
                    tw_current = lambda x: x.companyWorkers + x.contractorWorkers + x.nonResidentialWorkers,
                ).reset_index()

    return territory_totals

def generate_current_sim_default_counts(
    resources_current: pd.DataFrame,
    resources_default:pd.DataFrame
):
    
    current_totals = resources_current.groupby('territoryName').sum().assign(
                    tw_current = lambda x: x.companyWorkers + x.contractorWorkers + x.nonResidentialWorkers,
                    tw_current_round = lambda x: 10 * np.ceil(x.tw_current / 10) # round up to nearest 10
                ).reset_index()

    default_totals = resources_default.groupby('territoryName').sum().assign(
                    tw_default = lambda x: x.companyWorkers + x.contractorWorkers + x.nonResidentialWorkers,
                ).reset_index()

    territory_totals = pd.merge(default_totals, current_totals, how = 'left', on = ['territoryName'])
    territory_totals = territory_totals.fillna(0.0) 
    
    total_worker_counts = {
        t.territoryName:[ 
            max(50,(t.tw_current_round - 100)),
            max(75, (t.tw_current_round - 50)),
            t.tw_current,
            (t.tw_current_round + 50),
            (t.tw_current_round + 100),
            t.tw_default
            ]        
        for _, t in territory_totals.iterrows()
    }
    tw_count_df = pd.DataFrame.from_dict(
                            {i : total_worker_counts[i]
                            for i in  total_worker_counts.keys()},
                            orient='index').reset_index()
    Cols = ['territoryName','tw_m100','tw_m50','tw_current','tw_p50','tw_p100','tw_default']
    tw_count_df.columns = Cols

    return tw_count_df

def add_future_counts(
        df:pd.DataFrame,
        resources_future:pd.DataFrame,
        time_zone
) -> pd.DataFrame:

    df['tw_future'] = 0

    if resources_future is not None:

        future_totals = resources_future.assign(
            tw_future=safe_column(resources_future, 'companyWorkers') +
                      safe_column(resources_future, 'contractorWorkers') +
                      safe_column(resources_future, 'nonResidentialWorkers'),

            arrivalTime_central=pd.to_datetime(resources_future.arrivalTime)
            .dt.tz_localize(None)
            .apply(lambda ts: convert_timestamp_from_utc_to(ts, time_zone).tz_localize(None))
        ).reset_index()

        for _, row in future_totals.iterrows():
            mask = (df['Datetime_hour'] >= row['arrivalTime_central']) & (df['territoryName'] == row['territoryName'])
            df.loc[mask,'tw_future'] = df[mask]['tw_future'] + row['tw_future']
    
    return df

def add_pred_vars(
        df:pd.DataFrame,
        rolling_outages:pd.DataFrame,
        latest_active:pd.DataFrame,
        etrResults: pd.DataFrame,
        current_time:datetime,
        ListCountTotalWorker:list
) -> pd.DataFrame:
    
    stormStart =  pd.to_datetime(etrResults['starttime'].unique()[0])    
       
    if current_time == stormStart:
        rolling_outages['new_outages_roll'] = rolling_outages['new_outages_roll'] + sum(latest_active['activeIncidents'])
    
    rolling_outages['sys_new_outages_roll'] = rolling_outages['new_outages_roll'].sum()
    rolling_outages['ttl_out_gt_1k']        = rolling_outages['sys_new_outages_roll'] > 1000

    df = pd.merge(df, rolling_outages, how = 'left', on = ['territoryName'])
    
    df['hours_since_start']    = (df['Datetime_hour'] -stormStart) / np.timedelta64(1, 'h')
    df['hours_since_start']    = np.where(df['hours_since_start'] < 0, 0, df['hours_since_start'])

    # restored & active outages across all worker counts
    df[[('ro_' + re.sub('\.', '_', str(b))) for (b) in ListCountTotalWorker]] = 0
    df[[('ao_' + re.sub('\.', '_', str(b))) for (b) in ListCountTotalWorker]] = 0
    
    return df

def build_etr_data(
    df: pd.DataFrame,
    triggers: pd.DataFrame,
    resources_current: pd.DataFrame,
    resources_future: pd.DataFrame,
    resources_default: pd.DataFrame,
    time_zone: str,
    triggered_by:str
) -> pd.DataFrame:
    
    # add time-vars
    df = (
        df 
        .assign(
            Datetime_hour           = lambda x: pd.to_datetime(x.local_time).dt.tz_convert(None),
            date                    = lambda x: pd.to_datetime(x.Datetime_hour).dt.date,
            hour_of_day             = lambda x : x.Datetime_hour.dt.hour,
            sin_time                = lambda x: np.sin(2 * np.pi * (x.Datetime_hour.dt.hour * 60 + x.Datetime_hour.dt.minute) / 1440),
            cos_time                = lambda x: np.cos(2 * np.pi * (x.Datetime_hour.dt.hour * 60 + x.Datetime_hour.dt.minute) / 1440),
            sin_day                 = lambda x: np.sin(2 * np.pi * (x.Datetime_hour.dt.day_of_year / 366)),
            cos_day                 = lambda x: np.cos(2 * np.pi * (x.Datetime_hour.dt.day_of_year / 366)),
            cos_day_inv             = lambda x: (-1 * x.cos_day).clip(0, 1),
            meal_hours              = lambda x: np.where(x.hour_of_day.isin([9,10,11]), 1, 0),
        )
    )
    
    # add trigger vars
    df = (
        df
        .merge(triggers, how="inner", on="territoryName")
    )

    ### worker count vars
    if triggered_by == "userGeneratedSimulation":
        # get simulation mode worker counts
        tw_current_sim_default = generate_simmode_counts(resources_current)
        

    else:
        # get current, simulation, default worker counts
        tw_current_sim_default = generate_current_sim_default_counts(resources_current, resources_default)
        
    # merge
    df = (
        df
        .merge(tw_current_sim_default, how = 'inner', on = 'territoryName')
    )

    # add future workers
    df           = add_future_counts(df, resources_future, time_zone)
    
    # all counts
    df['tw_current_enroute'] = df['tw_current'] + df['tw_future']
    df           = df.fillna(0)

    tw_current_sim_default_all = pd.merge(
        tw_current_sim_default, 
        df.groupby('territoryName')['tw_current_enroute'].max().reset_index(),
        how = 'inner',
        on = 'territoryName')
    
    return (
        df
        .sort_values(by=["territoryName", "Datetime_hour"], ascending=True)
        .reset_index(drop=True)
    ), tw_current_sim_default_all

def build_etr_results(territoryNames, case, received_mode, worker_counts, time_zone):
    
    if received_mode == 'off':
        case_id = ''
        case_starttime = None
        case_proj_sc_d = {territory : None for territory in territoryNames}
        case_proj_sc = pd.DataFrame(case_proj_sc_d.items(), columns=['territoryName', 'etr_proj_received'])

    else:
        case_id = case['stormName'].unique()[0]
        case_starttime = case['stormStartDate'].unique()[0]
        if case['projectedRegionalRestoration'][0].items():
            case_proj_sc = pd.DataFrame(case['projectedRegionalRestoration'][0].items(), columns=['territoryName', 'etr_proj_received'])
        else:
            case_proj_sc_d = {territory : None for territory in territoryNames}
            case_proj_sc = pd.DataFrame(case_proj_sc_d.items(), columns=['territoryName', 'etr_proj_received'])

    storm_d = {
        t.territoryName:[case_id,case_starttime,pd.to_datetime(t.etr_proj_received)]
        for _, t in case_proj_sc.iterrows()
            }
    etrResults = pd.DataFrame.from_dict({i : storm_d[i]
                            for i in  storm_d.keys()},
                            orient='index').reset_index()
    Cols = ['territoryName','stormName','starttime','etr_proj_received']
    etrResults.columns = Cols

    # set projected etr & hours cols
    etrResults[['etr_proj_' + col for col in worker_counts.columns if 'tw' in col]] = None
    etrResults[['etr_hours_' + col for col in worker_counts.columns if 'tw' in col]] = None

    etrResults = pd.merge(etrResults, worker_counts, how = 'inner', on ='territoryName')

    if case_starttime is not None:
        etrResults = etrResults.assign(
                starttime = lambda x: pd.to_datetime(x.starttime).apply(
                    lambda x: convert_timestamp_from_utc_to(x, time_zone).tz_localize(None)
                ),
            ).reset_index()
    
    if all(etrResults.etr_proj_received):
        etrResults = etrResults.assign(
                etr_proj_received = lambda x: pd.to_datetime(x.etr_proj_received).apply(
                    lambda x: convert_timestamp_from_utc_to(x, time_zone).tz_localize(None)
                    )
            ).reset_index()

    return (
        etrResults
        .sort_values(by=["territoryName"], ascending=True)
        )

def build_base_df(
    current_time: datetime, 
    territories: list,
    time_zone: str="US/Central",
    window_size_hours: int=336
):
    # set up exhaustive analysis environment
    start = current_time
    end   = current_time + timedelta(hours=window_size_hours)

    times = pd.date_range(start=start, end=end,freq='1H')
    df_tmp_time = pd.DataFrame()
    df_tmp_time['local_time'] = times
    df_tmp_time['tmp_id'] = 0

    df_tmp_opc_zones = pd.DataFrame()
    df_tmp_opc_zones['territoryName'] = territories
    df_tmp_opc_zones['tmp_id'] = 0

    df_base = pd.DataFrame()
    df_base = df_tmp_opc_zones.merge(df_tmp_time,on='tmp_id')[['territoryName','local_time']]
    df_base = df_base\
        .assign(
            local_time= lambda x: pd.to_datetime(x.local_time).dt.tz_convert(None).apply(
                lambda x: convert_timestamp_from_utc_to(x, time_zone),
            )
        )

    return df_base

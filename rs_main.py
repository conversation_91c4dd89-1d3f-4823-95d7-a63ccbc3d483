from datetime import datetime
import logging
import os

import pandas as pd
from pytz import timezone, utc
import warnings; warnings.filterwarnings("ignore");

from ETRWriter.Writer import ETRSessionBuilder

from rs_utils import convert_datetime_to_utc

from rs_gather_data import ETRInputs
from rs_build_data import build_etr_data, build_etr_results, build_base_df, add_pred_vars
from rs_parameters import glm_params_d
from rs_storm_detection import start_detection
from rs_predict import predict, severe_weather_event_etr
from rs_write_outputs import write_results_to_mongo, write_summary_to_mongo, write_storm_entity, archive_storm_entity


CLIENT_TIME_ZONE = "US/Central"
territoryNames = ['Mobile', 'Anniston', 'Eufaula', 'Montgomery', 'Birmingham', 'Tuscaloosa']
window_size_hours = 336
storm_end_etr_col = 'etr_proj_tw_current'

logging.basicConfig(
    format="%(asctime)s %(levelname)-8s %(message)s",
    level=logging.INFO,
    datefmt="%Y-%m-%d %H:%M:%S",
)

def main(
    current_time: datetime,
    triggered_by: str,
    resource_simulation_id:str
):

    generationTime = current_time.replace(tzinfo=None)
    current_time = current_time.replace(minute=0, second=0, microsecond=0, tzinfo=None)

    logging.info("STARTING MODULE: data ingest & compilation")
    etr_inputs = ETRInputs("etr")
    
    logging.info("reading from mongo: open storms")
    storm_entity, storm_id, received_mode = etr_inputs.get_open_storms()

    logging.info("reading from mongo: resource inputs")
    resources_current = etr_inputs.get_current_resources(storm_id)
    resources_future  = etr_inputs.get_future_resources(storm_id)
    resources_default = etr_inputs.get_default_resources(storm_id)

    logging.info("reading from mongo: active outages")
    latest_active = etr_inputs.get_latest_active_outages(territoryNames)

    logging.info("reading from mongo: historical outages")
    rolling_outages = etr_inputs.get_historical_outages(territoryNames)

    logging.info("reading from mongo: threshold triggers")
    triggers = etr_inputs.get_thresholds()

    logging.info("building: exhaustive territory-hours across 14-day window")
    base_df = build_base_df(
        #current_time.tz_localize(utc), #TODO
        current_time.astimezone(utc), 
        territories = territoryNames,
        time_zone=CLIENT_TIME_ZONE,
        window_size_hours=window_size_hours
        )
    logging.info("building: etr dataset")
    etr_data, worker_counts = build_etr_data(
        base_df,
        triggers,
        resources_current,
        resources_future,
        resources_default,
        CLIENT_TIME_ZONE,
        triggered_by
    )
    
    tw_list = [col for col in worker_counts.columns if 'tw' in col]

    logging.info("building: storm entity for run")
    etrResults = build_etr_results(
        territoryNames, 
        storm_entity, 
        received_mode, 
        worker_counts,
        CLIENT_TIME_ZONE
    )
    logging.info("COMPLETED MODULE: data ingest & build")




    ### DETECTION MODULE ###
    # run on the hour if and only if received-mode != active
    # skip to etr-predict module if received-mode == active
    # update etrResults with results of start_detection & set mode
    # if mode != 'off', continue to predict-etr module
    # if mode == 'off', skip predict-etr module  
    logging.info("STARTING MODULE: storm detection")
    if (received_mode == 'active'):
        mode = 'active'
        logging.info("active storm detected, skipping detection")

    if (received_mode != 'active'):
        stime = start_detection(
            current_time,
            triggers,
            latest_active
        )
        # if invalid
        if (pd.isna(stime)) | (stime is None):
            mode = 'off'   
        # if valid
        else:
            if received_mode == 'off':
                # new storm
                etrResults['starttime'] = stime
                etrResults['stormName'] = pd.to_datetime(stime).strftime("%Y-%m-%d %H:%M:%S")
                # set mode for predict-etr module to ingest
                mode = 'active'

        logging.info("COMPLETED MODULE: storm detection")
  



    ### PREDICT/ETR MODULE ###
    # run hourly/by user if and only if mode != off
    # predict across hours & update etrResults with results of predict, severe_weather_event_etr
    # (include scenario runs if and only if run hourly (triggered_by='system')
    # set mode = "post-restoration" if all regions are restored at current_time
    logging.info("STARTING MODULE: predict/etr")
    
    storm_found = False
    storm_found_for_tracked_resources = False 
    
    if (mode == 'active'):

        # add storm-related vars (i.e. hours since start of storm) for predictions
        df = add_pred_vars(etr_data, rolling_outages, latest_active, etrResults,current_time, tw_list)

        # run severe weather event logic across window (by hour) 
        logging.info('begin predictions...')
        start = datetime.now() # TODO

        for hr in df['Datetime_hour'].unique():
            hr = pd.to_datetime(hr)
            predict(
                df,
                hr,
                current_time,
                glm_params_d,
                worker_counts,
                latest_active,
                tw_list
            )  
        time2run = datetime.now() - start
        logging.info(f'...finished predictions in {time2run}')

        logging.info('calculate etr')
        etrResults = severe_weather_event_etr(
            etrResults,
            df,
            current_time,
            tw_list
        )

        if (any(etrResults['stormName'])):
            storm_found = True
            storm_found_for_tracked_resources = True
            
            logging.info('checking if mode should be changed to post-restoration')
            if (pd.to_datetime(etrResults[storm_end_etr_col].max() ) <= current_time):
                
                mode = 'post-restoration'
        
        logging.info(f'COMPLETED MODULE: predict/etr')
    




    ### WRITE OUT ###
    logging.info("STARTING MODULE: write results to mongo")
    if storm_found_for_tracked_resources:
        stormStartDate = convert_datetime_to_utc(
            pd.to_datetime(etrResults["starttime"].unique()[0])
        )
        stormEndDate = convert_datetime_to_utc(
            pd.to_datetime(etrResults[storm_end_etr_col].max())
        )
    else:
        stormStartDate = datetime(1900, 1, 1, 0, 0)
        stormEndDate = datetime(1900, 1, 1, 0, 0)

    if storm_found:

        if triggered_by == "userGeneratedSimulation":
            session_type = "userGeneratedSimulation"
        else:
            session_type = "result"

            if received_mode == "off":
                is_create =True
            else:
                is_create =False

            logging.info('writing out storm record')
            storm_id = write_storm_entity(
                etrResults,
                stormStartDate,
                stormEndDate,
                worker_counts['tw_current'].sum(),
                mode,
                is_create,
                storm_end_etr_col,
                storm_id
            )

        logging.info(f'builing ETR Session with storm_id = {storm_id}')
        etr_session = ETRSessionBuilder(
                    sessionType=session_type,
                    triggeredBy=triggered_by,
                    stormStartDate=stormStartDate,
                    validationClient="APC",
                    #sessionMetaData = {"stormSimulationModeId":resource_simulation_id}
                    stormId = storm_id
                ).build()
        
        logging.info('writing out results to mongo ')
        write_results_to_mongo(
            etrResults,
            resources_current,
            latest_active,
            tw_list,
            generationTime,
            stormStartDate,
            etr_session,
            triggered_by,
            resource_simulation_id,
            storm_end_etr_col,
            storm_id
        )            
        if triggered_by != 'userGeneratedSimulation':
            logging.info('writing out summary to mongo ')
            write_summary_to_mongo(
                etrResults,
                df,
                generationTime,
                stormStartDate,
                stormEndDate,
                etr_session,
                triggered_by,
                resource_simulation_id,
                storm_end_etr_col,
            )
    
    if not storm_found:
        logging.info("No storm found for simulation")
    if not storm_found_for_tracked_resources:
        logging.info("No storm found for input resources")

    post_restoration_storms = etr_inputs.get_post_restoration_storms()
    
    if post_restoration_storms is not None:
        for _, storm in post_restoration_storms.iterrows():
            if storm["expirationDate"] <= convert_datetime_to_utc(current_time):
                archive_storm_entity(
                    storm["stormName"],
                    storm["stormStartDate"],
                    storm["stormEndDate"],
                    storm["projectedRegionalRestoration"],
                    int(worker_counts['tw_current'].sum())
                    )
    logging.info("COMPLETED MODULE: write results to mongo")


if __name__ == "__main__":
    start = datetime.now()
    current_time = datetime.now(timezone(CLIENT_TIME_ZONE))
    triggered_by = os.getenv("TRIGGERED_BY", default="system")#"system","userGeneratedSimulation"
    resource_simulation_id = os.getenv("RESOURCE_SIMULATION_ID", default=None) #'652405f7d9d9acc92a3f9a9c' "6542af276f83080873dd7734"

    if triggered_by == "userGeneratedSimulation":
        generation_type = 'simulation'
        
    else:
        generation_type = None

    main(
        current_time,
        triggered_by,
        resource_simulation_id
    )
    print(datetime.now() - start)
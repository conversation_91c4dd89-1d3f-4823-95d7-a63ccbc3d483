from datetime import datetime
import os

import pandas as pd
from rs_gather_data import ETRInputs

from ETRWriter.Writer import (
    ETRDocumentBuilder, ETRSessionBuilder, ETRSummaryDocumentBuilder, 
    LogDocumentBuilder, StormRecordDocumentBuilder
)
from ETRWriter.ETR import (
    OutageSummary, RegionalETR, SummaryETR, WeatherSummary, 
    ETRSession, append_storm_log, update_storm_record
)

from ETRReader.etr_client import ETRClient
from ETRReader.models import EventCommandDto

from rs_utils import convert_datetime_to_utc, select_logic

def write_results_to_mongo(
    etrResults: pd.DataFrame,
    resources_current: pd.DataFrame,
    latest_active:pd.DataFrame,
    tw_list:list,
    current_time: datetime,
    storm_start_time: datetime,
    etr_session: ETRSession,
    triggered_by:str,
    resource_simulation_id:str,
    storm_end_etr_col:str,
    storm_id:str
):

    current_time = convert_datetime_to_utc(current_time)

    etrResults = pd.merge(etrResults, latest_active, how = 'inner', on = ['territoryName'])

    with etr_session as s:
        if triggered_by == 'userGeneratedSimulation':
            etrType = 'userGeneratedSimulation'
            forecastMetadata = {"stormSimulationModeId":resource_simulation_id}
            session_id = resource_simulation_id
            systemResources = etrResults['tw_current_enroute'].sum()
            tw_list = ['tw_current_enroute']
        else:
            etrType = 'result'
            forecastMetadata = {}
            session_id = s.id
            systemResources = etrResults['tw_current'].sum()
            tw_list = tw_list

        allRegionalETRsList = []
        for _, row in etrResults.iterrows():
            for col in tw_list:
                display_region = row['territoryName'].title()
                if col == 'tw_current':
                    type = "current"
                else:
                    type = 'simulation'

                allRegionalETRsList.append(
                    RegionalETR(
                        location=display_region,
                        outageScale="medium",
                        activeOutages=float(row['activeIncidents']),
                        activeCustOutages=float(row['activeCustomerOutages']),
                        remainingOutages=float(row['activeIncidents']),
                        remainingCustOutages=int(row['activeCustomerOutages']),
                        projectedETR=convert_datetime_to_utc(row[f'etr_proj_{col}']),
                        type = type,
                        totalResources = int(row[col]),
                        resources = {'companyWorkers':row[col],
                                     'contractorWorkers':0,
                                     'nonResidentialWorkers':0}
                    )
                )
    
        s.add_document(
            ETRDocumentBuilder(
                session=session_id,
                generationTime=current_time,
                forecastMetadata=forecastMetadata,
                stormStartDate=storm_start_time,
                regionalETRs= allRegionalETRsList,
                systemResources={
                    'companyWorkers': int(systemResources),
                    'contractorWorkers': 0,
                    'nonResidentialWorkers': 0,
                },
                resources = [
                    {
                        'territoryId': row['territoryId'],
                        'territoryName':row['territoryName'],
                        'resources':{
                            'contractorWorkers':row['contractorWorkers'],
                            'companyWorkers':row['companyWorkers'],
                            'nonResidentialWorkers':row['nonResidentialWorkers']
                        },
                        'totalResources':row['companyWorkers'] + row['contractorWorkers'] + row['nonResidentialWorkers']
                    } 
                    for _,row in resources_current.iterrows()
                ],
                systemRestoration={
                    'medium' : convert_datetime_to_utc(etrResults[storm_end_etr_col].max()),
                },
                etrType = etrType,
                stormId= storm_id
            ).build()
        )

def get_region_summary_etr(
        page_1: pd.DataFrame, 
        page_2: pd.DataFrame, 
        region: str,
        storm_end_etr_col:str
):
    
    region_df = page_2[page_2["territoryName"] == region].copy()
    
    weather = []
    outages = []
    
    for _, row in region_df.iterrows():
        forecast_datetime = convert_datetime_to_utc(row["Datetime_hour"])
        weather_summary = WeatherSummary(
            forecastDateTime=forecast_datetime,
            wind_mph=0,
            gust_mph=0,
            precip_inch=0
        )
        outage_summary = OutageSummary(
            forecastDateTime=forecast_datetime,
            activeOutages=row["ao_tw_current"],
            restoredOutages=row["ro_tw_current"],
            newOutages=0
        )
        weather.append(weather_summary)
        outages.append(outage_summary)

    derived_start_date = convert_datetime_to_utc(select_logic(page_1[page_1['territoryName']==region]["starttime"].unique()))
    derived_end_date = convert_datetime_to_utc(select_logic(page_1[page_1['territoryName']==region][storm_end_etr_col].unique()))

    display_region = row['territoryName'].title()

    return SummaryETR(
        displayName=display_region,
        derivedStartDate=derived_start_date,
        derivedEndDate=derived_end_date,
        weather=weather,
        outages=outages
    )

def write_summary_to_mongo(
    page_1: pd.DataFrame,
    page_2: pd.DataFrame,
    current_time: datetime,
    storm_start_time: datetime,
    storm_end_time: datetime,
    etr_session: ETRSession,
    triggered_by:str,
    resource_simulation_id:str,
    storm_end_etr_col:str
):

    region_summary_list = [
        get_region_summary_etr(page_1, page_2, region, storm_end_etr_col) 
        for region in page_2["territoryName"].unique()
    ]
    
    with etr_session as s:
        if triggered_by == 'userGeneratedSimulation':
            session_id = resource_simulation_id
        else:
            session_id = s.id
    
        s.add_document(
            ETRSummaryDocumentBuilder(
                session=session_id,
                stormStart=storm_start_time,
                stormEnd=storm_end_time,
                generationTime=convert_datetime_to_utc(current_time),
                regionalSummary=region_summary_list
            ).build()
        )

def _create_log(
    storm_start_time: datetime,
    storm_end_time: datetime,
    is_etr_found: bool,
    worker_count: int,
    mode: str
) -> LogDocumentBuilder:
    return LogDocumentBuilder(
        stormStartDate=storm_start_time,
        stormEndDate=storm_end_time,
        etrFound=is_etr_found,
        workerCount=worker_count,
        stormMode=mode,
    )

def _create_storm_entity(
    storm_start_time: datetime,
    storm_end_time: datetime,
    mode: str,
    projected_region_etr: dict,
    is_etr_found: bool,
    worker_count: int,
):
    session = ETRSessionBuilder(sessionType="storm_record").build()
    log = _create_log(
        storm_start_time,
        storm_end_time,
        is_etr_found,
        worker_count,
        mode
    )

    with session as s:
        storm_name = "Storm Event: " + pd.to_datetime(storm_start_time).strftime("%Y-%m-%d %H:%M:%S")
        storm_record_document = StormRecordDocumentBuilder(
            session=str(session.id),
            stormName=storm_name,
            stormStartDate=storm_start_time,
            stormEndDate=storm_end_time,
            stormMode=mode,
            generationType="system",
            projectedRegionalRestoration=projected_region_etr,
            metadata = {
                "log": [log.build()]
            }
        )
        s.add_document(storm_record_document.build())

    etr_inputs = ETRInputs("etr")
    _, storm_id, _ = etr_inputs.get_open_storms()

    dto = EventCommandDto(event="startStorm", data={"stormId": storm_id, "stormDisplayName": storm_name})
    etr_client = ETRClient()
    etr_client.send_event(dto)

    return storm_id

def _update_storm_entity(
    storm_name: str,
    storm_start_time: datetime,
    storm_end_time: datetime,
    mode: str,
    projected_region_etr: dict,
    is_etr_found: bool,
    worker_count: int,
):
    log = _create_log(
        storm_start_time,
        storm_end_time,
        is_etr_found,
        worker_count,
        mode
    )
    update_storm_record(
        storm_name=storm_name,
        stormStartDate=storm_start_time,
        stormEndDate=storm_end_time,
        stormMode=mode,
        projectedRegionalRestoration=projected_region_etr,
        metadata={'example':'example'} #TODO
    )

    append_storm_log(storm_name, log.build())

def write_storm_entity(
    storm_df: pd.DataFrame,
    storm_start_time: datetime,
    etr_time: datetime,
    worker_count: int,
    mode: str,
    is_create: bool,
    storm_end_etr_col:str,
    storm_id:str
):
    new_storm_id = None

    projected_region_etr = {
        row["territoryName"]:convert_datetime_to_utc(row[storm_end_etr_col])
        for _, row in storm_df.iterrows()
    }
    if (pd.isna(etr_time)) | (etr_time is None):
        is_etr_found = False
        etr_time = None
    else:
        is_etr_found = True

    if is_create:
        new_storm_id = _create_storm_entity(
            storm_start_time,
            etr_time,
            mode,
            projected_region_etr,
            is_etr_found,
            worker_count
        )
    else:
        _update_storm_entity(
            storm_df['stormName'].unique()[0],
            storm_start_time,
            etr_time,
            mode,
            projected_region_etr,
            is_etr_found,
            worker_count,
        )
    if new_storm_id is not None:
        storm_id = new_storm_id

    return storm_id
    
def archive_storm_entity(
    storm_name: str,
    storm_start_time:datetime,
    storm_end_time:datetime,
    projected_region_etr:dict,
    worker_count:int,
):
    
    _update_storm_entity(
            storm_name = storm_name,
            storm_start_time = storm_start_time,
            storm_end_time=storm_end_time,
            projected_region_etr = projected_region_etr,
            worker_count = worker_count,
            mode = 'archive',
            is_etr_found = True
    )
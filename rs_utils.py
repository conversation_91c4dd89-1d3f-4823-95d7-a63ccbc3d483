from datetime import datetime, timedelta
import logging
import os
from typing import List, Union

import numpy as np
import pandas as pd
from pandas import Timestamp
from pytz import timezone, utc
import pytz

def convert_timestamp_from_utc_to(
    x: Timestamp, to_tz: str="US/Central"
) -> Timestamp:
    if (pd.isna(x)) | (x is None):
        x = None
    else:
        x = x.tz_localize(utc).astimezone(to_tz)
    return x

def get_temporal_folder_path_from_datetime(x: datetime) -> str:
    return x.strftime("year=%Y/month=%m/day=%d/hour=%H")

def get_s3_temporal_file_path(
    s3_uri_base: str, 
    current_time: datetime
) -> str:
    temporal_folder_path = get_temporal_folder_path_from_datetime(current_time)
    return os.path.join(s3_uri_base, temporal_folder_path)

def convert_datetime_to_utc(
    x: Union[str, Timestamp, datetime], 
    from_tz="US/Central"
) -> Timestamp:
    if (pd.isna(x)) | (x is None):
        x = None
    else:
        if not isinstance(x, Timestamp):
            x = pd.to_datetime(x)
        try:
            x = x.tz_localize(from_tz,ambiguous=True).astimezone(utc).replace(tzinfo=None)
        except pytz.exceptions.NonExistentTimeError as e:
            logging.warning(e)
            x = (x + timedelta(hours=1)).tz_localize(from_tz,ambiguous=True).astimezone(utc).replace(tzinfo=None)
    return x

def select_logic(x: List[Timestamp]) -> Timestamp:
    if len(x) == 0:
        return x[0]
    return sorted(x, reverse=True)[0]

def safe_column(df, col):
    return df[col].fillna(0) if col in df else pd.Series(0, index=df.index)

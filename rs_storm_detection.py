from datetime import datetime

import numpy as np
import pandas as pd


def start_detection(
            current_time,
            triggers,
            latest_active
):
    
    stime = severe_weather_event_start(current_time,triggers,latest_active)

    return stime

def severe_weather_event_start(
        current_time,
        triggers,
        latest_active
):
    
    stime = None

    compare_thresh_df = pd.merge(triggers, latest_active, how = 'inner', on = ['territoryName'])
    
    sum_sc_elevated_active_outs = (compare_thresh_df['activeIncidents'].values >= compare_thresh_df['thresholdIncidents']).sum()
    sum_sc_elevated_active_outs_2x = (compare_thresh_df['activeIncidents'].values >= compare_thresh_df['thresholdIncidents']).sum()*2
    sum_sc_elevated_active_custouts = (compare_thresh_df['activeCustomerOutages'].values >= compare_thresh_df['thresholdOutages']).sum()
    sum_sc_elevated_active_custouts_2x = (compare_thresh_df['activeCustomerOutages'].values >= compare_thresh_df['thresholdOutages']).sum()*2

    if (sum_sc_elevated_active_outs >= 2) | (sum_sc_elevated_active_outs_2x >= 1):
        
        stime = current_time


    return stime
